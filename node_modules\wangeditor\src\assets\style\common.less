.w-e-toolbar,
.w-e-text-container,
.w-e-menu-panel {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    background-color: #fff;
    h1 {
        font-size: 32px !important;
    }
    h2 {
        font-size: 24px !important;
    }
    h3 {
        font-size: 18.72px !important;
    }
    h4 {
        font-size: 16px !important;
    }
    h5 {
        font-size: 13.28px !important;
    }
    p {
        font-size: 16px !important;
    }
    /*表情菜单样式*/
    .eleImg{
        cursor: pointer;
        display: inline-block;
        font-size: 18px;
        padding: 0 3px;
    }
    * {
        padding: 0;
        margin: 0;
        box-sizing: border-box;
    }
    /*分割线样式*/
    hr{
      cursor: pointer;
      display: block;
      height: 0px;
      border: 0;
      border-top: 3px solid #ccc;
      margin: 20px 0;
    }
}

.w-e-clear-fix:after {
    content: "";
    display: table;
    clear: both;
}

.w-e-drop-list-item {
    position: relative;
    top: 1px;
    padding-right: 7px;
    color: #333 !important;
}

.w-e-drop-list-tl {
    padding-left: 10px;
    text-align: left;
}
