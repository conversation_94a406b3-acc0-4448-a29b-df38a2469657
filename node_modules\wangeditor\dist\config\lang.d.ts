declare const _default: {
    lang: string;
    languages: {
        'zh-CN': {
            wangEditor: {
                重置: string;
                插入: string;
                默认: string;
                创建: string;
                修改: string;
                如: string;
                请输入正文: string;
                menus: {
                    title: {
                        标题: string;
                        加粗: string;
                        字号: string;
                        字体: string;
                        斜体: string;
                        下划线: string;
                        删除线: string;
                        缩进: string;
                        行高: string;
                        文字颜色: string;
                        背景色: string;
                        链接: string;
                        序列: string;
                        对齐: string;
                        引用: string;
                        表情: string;
                        图片: string;
                        视频: string;
                        表格: string;
                        代码: string;
                        分割线: string;
                        恢复: string;
                        撤销: string;
                        全屏: string;
                        取消全屏: string;
                        待办事项: string;
                    };
                    dropListMenu: {
                        设置标题: string;
                        背景颜色: string;
                        文字颜色: string;
                        设置字号: string;
                        设置字体: string;
                        设置缩进: string;
                        对齐方式: string;
                        设置行高: string;
                        序列: string;
                        head: {
                            正文: string;
                        };
                        indent: {
                            增加缩进: string;
                            减少缩进: string;
                        };
                        justify: {
                            靠左: string;
                            居中: string;
                            靠右: string;
                            两端: string;
                        };
                        list: {
                            无序列表: string;
                            有序列表: string;
                        };
                    };
                    panelMenus: {
                        emoticon: {
                            默认: string;
                            新浪: string;
                            emoji: string;
                            手势: string;
                        };
                        image: {
                            上传图片: string;
                            网络图片: string;
                            图片地址: string;
                            图片文字说明: string;
                            跳转链接: string;
                        };
                        link: {
                            链接: string;
                            链接文字: string;
                            取消链接: string;
                            查看链接: string;
                        };
                        video: {
                            插入视频: string;
                            上传视频: string;
                        };
                        table: {
                            行: string;
                            列: string;
                            的: string;
                            表格: string;
                            添加行: string;
                            删除行: string;
                            添加列: string;
                            删除列: string;
                            设置表头: string;
                            取消表头: string;
                            插入表格: string;
                            删除表格: string;
                        };
                        code: {
                            删除代码: string;
                            修改代码: string;
                            插入代码: string;
                        };
                    };
                };
                validate: {
                    张图片: string;
                    大于: string;
                    图片链接: string;
                    不是图片: string;
                    返回结果: string;
                    上传图片超时: string;
                    上传图片错误: string;
                    上传图片失败: string;
                    插入图片错误: string;
                    一次最多上传: string;
                    下载链接失败: string;
                    图片验证未通过: string;
                    服务器返回状态: string;
                    上传图片返回结果错误: string;
                    请替换为支持的图片类型: string;
                    您插入的网络图片无法识别: string;
                    您刚才插入的图片链接未通过编辑器校验: string;
                    插入视频错误: string;
                    视频链接: string;
                    不是视频: string;
                    视频验证未通过: string;
                    个视频: string;
                    上传视频超时: string;
                    上传视频错误: string;
                    上传视频失败: string;
                    上传视频返回结果错误: string;
                };
            };
        };
        en: {
            wangEditor: {
                重置: string;
                插入: string;
                默认: string;
                创建: string;
                修改: string;
                如: string;
                请输入正文: string;
                menus: {
                    title: {
                        标题: string;
                        加粗: string;
                        字号: string;
                        字体: string;
                        斜体: string;
                        下划线: string;
                        删除线: string;
                        缩进: string;
                        行高: string;
                        文字颜色: string;
                        背景色: string;
                        链接: string;
                        序列: string;
                        对齐: string;
                        引用: string;
                        表情: string;
                        图片: string;
                        视频: string;
                        表格: string;
                        代码: string;
                        分割线: string;
                        恢复: string;
                        撤销: string;
                        全屏: string;
                        取消全屏: string;
                        待办事项: string;
                    };
                    dropListMenu: {
                        设置标题: string;
                        背景颜色: string;
                        文字颜色: string;
                        设置字号: string;
                        设置字体: string;
                        设置缩进: string;
                        对齐方式: string;
                        设置行高: string;
                        序列: string;
                        head: {
                            正文: string;
                        };
                        indent: {
                            增加缩进: string;
                            减少缩进: string;
                        };
                        justify: {
                            靠左: string;
                            居中: string;
                            靠右: string;
                            两端: string;
                        };
                        list: {
                            无序列表: string;
                            有序列表: string;
                        };
                    };
                    panelMenus: {
                        emoticon: {
                            表情: string;
                            手势: string;
                        };
                        image: {
                            上传图片: string;
                            网络图片: string;
                            图片地址: string;
                            图片文字说明: string;
                            跳转链接: string;
                        };
                        link: {
                            链接: string;
                            链接文字: string;
                            取消链接: string;
                            查看链接: string;
                        };
                        video: {
                            插入视频: string;
                            上传视频: string;
                        };
                        table: {
                            行: string;
                            列: string;
                            的: string;
                            表格: string;
                            添加行: string;
                            删除行: string;
                            添加列: string;
                            删除列: string;
                            设置表头: string;
                            取消表头: string;
                            插入表格: string;
                            删除表格: string;
                        };
                        code: {
                            删除代码: string;
                            修改代码: string;
                            插入代码: string;
                        };
                    };
                };
                validate: {
                    张图片: string;
                    大于: string;
                    图片链接: string;
                    不是图片: string;
                    返回结果: string;
                    上传图片超时: string;
                    上传图片错误: string;
                    上传图片失败: string;
                    插入图片错误: string;
                    一次最多上传: string;
                    下载链接失败: string;
                    图片验证未通过: string;
                    服务器返回状态: string;
                    上传图片返回结果错误: string;
                    请替换为支持的图片类型: string;
                    您插入的网络图片无法识别: string;
                    您刚才插入的图片链接未通过编辑器校验: string;
                    插入视频错误: string;
                    视频链接: string;
                    不是视频: string;
                    视频验证未通过: string;
                    个视频: string;
                    上传视频超时: string;
                    上传视频错误: string;
                    上传视频失败: string;
                    上传视频返回结果错误: string;
                };
            };
        };
    };
};
export default _default;
