.w-e-menu {
    .w-e-panel-container {
        position: absolute;
        top: 0;
        left: 50%;
        border: 1px solid #ccc;
        border-top: 0;
        box-shadow: 1px 1px 2px #ccc;
        color: #333;
        background-color: #fff;
        text-align: left;

        .w-e-panel-close {
            position: absolute;
            right: 0;
            top: 0;
            padding: 5px;
            margin: 2px 5px 0 0;
            cursor: pointer;
            color: #999;

            &:hover {
                color: #333;
            }
        }

        .w-e-panel-tab-title {
            list-style: none;
            display: flex;
            font-size: 14px;
            margin: 2px 10px 0 10px;
            border-bottom: 1px solid #f1f1f1;

            .w-e-item {
                padding: 3px 5px;
                color: #999;
                cursor: pointer;
                margin: 0 3px;
                position: relative;
                top: 1px;
            }

            .w-e-active {
                color: #333;
                border-bottom: 1px solid #333;
                cursor: default;
                font-weight: 700;
            }
        }

        .w-e-panel-tab-content {
            padding: 10px 15px 10px 15px;
            font-size: 16px;

            /* 输入框的样式 */
            input,textarea,button {
                &:focus {
                    outline: none;
                }   
            }
            textarea {
                width: 100%;
                border: 1px solid #ccc;
                padding: 5px;
                margin-top: 10px;

                &:focus {
                    border-color: #1e88e5;
                }
            }
            input[type=text] {
                border: none;
                border-bottom: 1px solid #ccc;
                font-size: 14px;
                height: 20px;
                color: #333;
                text-align: left;
            }
            input[type=text].small {
                width: 30px;
                text-align: center;
            }

            input[type=text].block {
                display: block;
                width: 100%;
                margin: 10px 0;
            }

            input[type=text]:focus {
                border-bottom: 2px solid #1e88e5;
            }

            /* 按钮的样式 */
            .w-e-button-container {
                button {
                    font-size: 14px;
                    color: #1e88e5;
                    border: none;
                    padding: 5px 10px;
                    background-color: #fff;
                    cursor: pointer;
                    border-radius: 3px;
                }
                button.left {
                    float: left;
                    margin-right: 10px;
                }

                button.right {
                    float: right;
                    margin-left: 10px;
                }

                button.gray {
                    color: #999;
                }

                button.red {
                    color: rgb(194, 79, 74);
                }

                button:hover {
                    background-color: #f1f1f1;
                }
            } 
            .w-e-button-container:after {
                content: "";
                display: table;
                clear: both;
            }
        }

        /* 为 emotion panel 定制的样式 */
        .w-e-emoticon-container {
            .w-e-item {
                cursor: pointer;
                font-size: 18px;
                padding: 0 3px;
                display: inline-block;
            }
        }

        /* 上传图片、上传视频的 panel 定制样式 */
        .w-e-up-img-container, .w-e-up-video-container {
            text-align: center;

            .w-e-up-btn {
                display: inline-block;
                color: #999;
                cursor: pointer;
                font-size: 60px;
                line-height: 1;

                &:hover {
                    color: #333;
                }
            }
        }
    }
}