import $, { DomElement } from '../../../utils/dom-core'
import WrapListHandle from './WrapListHandle'
import Join<PERSON><PERSON><PERSON><PERSON><PERSON> from './JoinListHandle'
import StartJoinListHandle from './StartJoinListHandle'
import <PERSON><PERSON>oin<PERSON>istHandle from './EndJoinListHandle'
import Other<PERSON>istHandle from './OtherListHandle'

import { HandlerListOptions } from './ListHandle'

// 片段类型
export type ContainerFragment = HTMLElement | DocumentFragment

// 处理类
export type ListHandleClass =
    | WrapListHandle
    | JoinListHandle
    | StartJoinListHandle
    | EndJoinListHandle
    | OtherListHandle

export enum ClassType {
    Wrap = 'WrapListHandle',
    Join = 'JoinListHandle',
    StartJoin = 'StartJoinListHandle',
    EndJoin = 'EndJoinListHandle',
    Other = 'OtherListHandle',
}

const handle = {
    WrapListHandle,
    Join<PERSON><PERSON><PERSON>and<PERSON>,
    StartJoin<PERSON>istHandle,
    EndJoin<PERSON>istHandle,
    OtherListHandle,
}

export function createListHandle(
    classType: ClassType,
    options: HandlerListOptions,
    range?: Range
): ListHandleClass {
    if (classType === ClassType.Other && range === undefined) {
        throw new Error('other 类需要传入 range')
    }

    return classType !== ClassType.Other
        ? new handle[classType](options)
        : new handle[classType](options, range as Range)
}

/**
 * 统一执行的接口
 */
export default class ListHandleCommand {
    private handle: ListHandleClass

    constructor(handle: ListHandleClass) {
        this.handle = handle
        this.handle.exec()
    }

    getSelectionRangeElem(): DomElement {
        return $(this.handle.selectionRangeElem.get())
    }
}
