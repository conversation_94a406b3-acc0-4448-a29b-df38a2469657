## 4.7.15 (2022-04-12)


### Bug Fixes

* update README ([e12a0dc](https://github.com/wangeditor-team/wangEditor/commit/e12a0dc386627454ee0ea4e7137a1222a43d52c1))

## [4.7.13](https://github.com/wangeditor-team/wangEditor/compare/v4.7.12...v4.7.13) (2022-04-04)

## [4.7.12](https://github.com/wangeditor-team/wangEditor/compare/v4.7.11...v4.7.12) (2022-02-25)


### Bug Fixes

* 链接修改字号后再点链接按钮无法看到链接地址 ([b396a1e](https://github.com/wangeditor-team/wangEditor/commit/b396a1e42cab307a6681ca03321b58162bfc396c))
* 修复UI无序列表全选不居中问题 ([158628d](https://github.com/wangeditor-team/wangEditor/commit/158628d3e9a80eab910334d0ad4f5a43dad2f0e8))
* url或者常规文本中HTML转义字符显示异常 ([03310ee](https://github.com/wangeditor-team/wangEditor/commit/03310eedc40283c2e228281f1f7eeea9c06c8fa9))


### Features

* xss ([6257a2e](https://github.com/wangeditor-team/wangEditor/commit/6257a2e166346913c34ac5cfb31b6a46e9544c5a))

## [4.7.11](https://github.com/wangeditor-team/wangEditor/compare/v4.7.10...v4.7.11) (2021-12-25)


### Bug Fixes

* 插入链接后再修改的问题 ([fcaef96](https://github.com/wangeditor-team/wangEditor/commit/fcaef96983e764d0014238d498531344526c3345))
* 删除文字的时候前一张图片不会被删除 ([7c63218](https://github.com/wangeditor-team/wangEditor/commit/7c63218812604c8791f8ab06b656fa197779d2a6))
* 删除文字的时候前一张图片不会被删除 ([bac1b1e](https://github.com/wangeditor-team/wangEditor/commit/bac1b1ea2b597d3c7240506258703eb1792ec2c9))
* 修复选中段落设置行高，最后一行的内容会被重复添加的问题 ([46e49a9](https://github.com/wangeditor-team/wangEditor/commit/46e49a92e84f5dc29a67386f5b3d41dad29b41a5))
* delete text will delete img too ([2d060d5](https://github.com/wangeditor-team/wangEditor/commit/2d060d5fb16ee1edf281383acf99299c0971f47c))

## [4.7.10](https://github.com/wangeditor-team/wangEditor/compare/v4.7.9...v4.7.10) (2021-12-01)


### Bug Fixes

* 复制单张图片无效 ([698c013](https://github.com/wangeditor-team/wangEditor/commit/698c013445a63caba9ea690227cacc1d947348fe))

## [4.7.9](https://github.com/wangeditor-team/wangEditor/compare/v4.7.8...v4.7.9) (2021-10-17)


### Bug Fixes

* 修复谷歌内容为空时拼音输入第一个字母丢失的bug ([52301c1](https://github.com/wangeditor-team/wangEditor/commit/52301c127b10b78847f4825a803f49c9195b041a))
* 修复内容随后设置标题，会报错，而且无法再编辑的问题 ([5022ceb](https://github.com/wangeditor-team/wangEditor/commit/5022ceb2ee97f8aed87427b0fb363c5262fad2a5))
* 修复设置标题控制台报错的bug ([f4680b2](https://github.com/wangeditor-team/wangEditor/commit/f4680b2634679ffd3d0186f24e713438fcc907d0))

## [4.7.8](https://github.com/wangeditor-team/wangEditor/compare/v4.7.7...v4.7.8) (2021-09-09)


### Bug Fixes

* 修复表格中选中文字插入链接多出table标签和chrome浏览器表格高度问题 ([325f1f6](https://github.com/wangeditor-team/wangEditor/commit/325f1f6f72d305de1ff0b5873951e88c4d40d5b5))

## [4.7.7](https://github.com/wangeditor-team/wangEditor/compare/v4.7.6...v4.7.7) (2021-08-20)


### Bug Fixes

* 清空内容后-点击工具栏上插入链接的按钮，无反应 ([c1340d1](https://github.com/wangeditor-team/wangEditor/commit/c1340d17b24f9f61974bd122602cd7485c4453a2))
* 图片添加链接直接删除防止报错 ([2ce2639](https://github.com/wangeditor-team/wangEditor/commit/2ce2639df10cf88f7b44618202136342ed683e86))
* 无内容的情况下粘贴一段文本，粘贴后光标位于行首而不是粘贴内容的行尾 ([62d5c4a](https://github.com/wangeditor-team/wangEditor/commit/62d5c4a93f243f20f0269976432e585fbb7d39b1))
* 修复低版本内核粘贴单张图报错 ([96f2f8d](https://github.com/wangeditor-team/wangEditor/commit/96f2f8de4915a223619d9f77902194a7e1dd3ae4))


### Features

* 修复当编辑区没内容时,上传一张图片,删除后鼠标位置的问题 ([2fec14e](https://github.com/wangeditor-team/wangEditor/commit/2fec14e6c66442d3860052a9d0869a15d86165ee))

## [4.7.6](https://github.com/wangeditor-team/wangEditor/compare/v4.7.5...v4.7.6) (2021-07-30)


### Bug Fixes

* 解决全选删除后，设置引用，全屏的问题 ([514d563](https://github.com/wangeditor-team/wangEditor/commit/514d56323442685ce1a80faf911208c635daf5ab))
* 修复列表下无法设置超链接的问题 ([00bfdb5](https://github.com/wangeditor-team/wangEditor/commit/00bfdb5bc20e00cd2d632de45a6d9c7b3a2cea67))

## [4.7.5](https://github.com/wangeditor-team/wangEditor/compare/v4.7.4...v4.7.5) (2021-07-02)


### Bug Fixes

* 涉及到有插入按钮的菜单, 键盘输入enter键，也能触发事件 ([2fdef65](https://github.com/wangeditor-team/wangEditor/commit/2fdef658ca6fb22b5fc635e7c0a9310dfbcde0ec))
* 修复ie11序列功能无效的bug ([dd8bdaf](https://github.com/wangeditor-team/wangEditor/commit/dd8bdafa3d180f7c4e1e39f9a0e7f276d4b723de))


### Features

* add onselectionchange api ([afa36a8](https://github.com/wangeditor-team/wangEditor/commit/afa36a833e62a593f056cea9bc760fa46c930708))

## [4.7.4](https://github.com/wangeditor-team/wangEditor/compare/v4.7.3...v4.7.4) (2021-06-17)


### Bug Fixes

* 修复行内code回车后导致样式及插入问题 ([cebf20a](https://github.com/wangeditor-team/wangEditor/commit/cebf20a79df1f979376f27eee128ae3c95a4a4eb))
* 修复a标签包裹img的 取消链接 问题 ([23eb557](https://github.com/wangeditor-team/wangEditor/commit/23eb557a5dabcdc6201753695a8590dec539fe9e))

## [4.7.3](https://github.com/wangeditor-team/wangEditor/compare/v4.7.2...v4.7.3) (2021-06-10)


### Bug Fixes

* 特殊情况下无法通过backspace删除图片 ([0648a93](https://github.com/wangeditor-team/wangEditor/commit/0648a9340a7c156dbc16b83e955f684854cb8c76))
* 优化粘贴时过滤meta的行为 ([313665c](https://github.com/wangeditor-team/wangEditor/commit/313665c5a656314477f8973e0cdff9c4ba125aeb))

## [4.7.2](https://github.com/wangeditor-team/wangEditor/compare/v4.7.1...v4.7.2) (2021-06-03)


### Bug Fixes

* 禁修复多行插入链接造成页面卡死,止多行插入链链接 ([110b37a](https://github.com/wangeditor-team/wangEditor/commit/110b37a8bb3b6bd5d87e1d831c8dab6f52e2b6df))
* 设置字体大小和颜色时，光标跳到行首 ([8fc7e04](https://github.com/wangeditor-team/wangEditor/commit/8fc7e04272ab222416e2cbfbaa57eb2565a8f87a))
* 修复给标题加超链接的问题 ([0d7a05e](https://github.com/wangeditor-team/wangEditor/commit/0d7a05e8c922bc48dc655bee90b8192a816057f9))
* 修复删除全部内容后EMPTY_P前有个空格 ([1811f8c](https://github.com/wangeditor-team/wangEditor/commit/1811f8c417f8466df5d458a6c215fad1e4b2b7c3))
* ts 构建错误 ([4603677](https://github.com/wangeditor-team/wangEditor/commit/46036774c73f909dae99c950b0ffcc67f7414b9f))

## [4.7.1](https://github.com/wangeditor-team/wangEditor/compare/v4.7.0...v4.7.1) (2021-05-20)


### Bug Fixes

* 设置/取消表头不删除第一行 ([d9fd913](https://github.com/wangeditor-team/wangEditor/commit/d9fd9139a2943b87193e6f37266fde94cbddab8e))
* 文本删完后存在的图片会消失 ([5b14868](https://github.com/wangeditor-team/wangEditor/commit/5b14868cc5ef2534476b9f81b35178108cf74b81))
* indent 缩进时, 增加对 head 的处理 ([8d506e4](https://github.com/wangeditor-team/wangEditor/commit/8d506e43afa016dcb7d618cbbf4c99658af0c670))

# [4.7.0](https://github.com/wangeditor-team/wangEditor/compare/v4.6.17...v4.7.0) (2021-05-13)


### Bug Fixes

* 全选删除表格内容,表格外的内容会进入表格 ([33aae89](https://github.com/wangeditor-team/wangEditor/commit/33aae8924f965cb412e76fb488baa84521a11ff2))
* 无法粘贴从qq复制过来的文本 ([fc88652](https://github.com/wangeditor-team/wangEditor/commit/fc88652aa0b2da381365b9aebde16a8ed6082f9e))
* 修复禁用状态下scroll-to-head功能失效 ([79d1326](https://github.com/wangeditor-team/wangEditor/commit/79d13267cb4616e1df88259477c3dd053b1de4e9))
* 修复ie11下 设置行高无效、多行无效和内容消失 ([b74f787](https://github.com/wangeditor-team/wangEditor/commit/b74f78731ec62f76a80ef00991cfa788c20830d3))
* 修复ie11序列功能无效的bug ([b4ecdbd](https://github.com/wangeditor-team/wangEditor/commit/b4ecdbd497bbc26a14032afedc0f9b21aad84954))
* 修复panel菜单position位置的问题 ([c66fb8b](https://github.com/wangeditor-team/wangEditor/commit/c66fb8b03623b3580d03bfe1f60e3ea266125a19))


### Features

* 插件注册和初始化 ([00326b6](https://github.com/wangeditor-team/wangEditor/commit/00326b60b83f1f0c2a0299182632479a88907a31))

## [4.6.17](https://github.com/wangeditor-team/wangEditor/compare/v4.6.16...v4.6.17) (2021-04-29)


### Bug Fixes

* 低版本谷歌粘贴图片报错 ([46dc441](https://github.com/wangeditor-team/wangEditor/commit/46dc44112067a44e555a2592a6827be5d3c8602b))
* 解决全选后删除光标停留触发控制error的bug ([ad61531](https://github.com/wangeditor-team/wangEditor/commit/ad61531d1dd13280b883a0b23c9839098c8dc1d3))
* 修复默认todo不触发change的问题 ([08696c9](https://github.com/wangeditor-team/wangEditor/commit/08696c9e37277be9adbe17d3b0a42ab51d683c42))
* 修复全选删除文本无法删除干净的问题 ([cb6a9c9](https://github.com/wangeditor-team/wangEditor/commit/cb6a9c960e80488f2e2fcf6fb7abc75fe4123642))
* 修复图片插入后修改样式后的光标位置 ([00539db](https://github.com/wangeditor-team/wangEditor/commit/00539db4cad2e5b2a3fa2902485b96ebcc2184e9))
* 修复placeholder中不能设置.的问题 ([e7aaf2d](https://github.com/wangeditor-team/wangEditor/commit/e7aaf2d764f2d5ac154bbb814e56b5195f839d23))
* lint ([c3e93eb](https://github.com/wangeditor-team/wangEditor/commit/c3e93eb23698973f5c65a1150367bb0695b43dbe))


### Performance Improvements

* entrypoint from 261kb to 219kb ([809559b](https://github.com/wangeditor-team/wangEditor/commit/809559b08b8729e1f5e0d1063c0a74425a13db77))
* entrypoint from 261kb to 219kb ([fc107b3](https://github.com/wangeditor-team/wangEditor/commit/fc107b36749db2396dcfb198dc8d5dee4b05bf50))
* entrypoint from 261kb to 219kb ([24a6fb0](https://github.com/wangeditor-team/wangEditor/commit/24a6fb0278bec002eb407a651731536385302c09))

## [4.6.16](https://github.com/wangeditor-team/wangEditor/compare/v4.6.15...v4.6.16) (2021-04-22)


### Bug Fixes

* 火狐浏览器剪切后保留p标签的bug. ([5e044e3](https://github.com/wangeditor-team/wangEditor/commit/5e044e3351349aaa900af5d6b960f41462edaff0))
* 使用em样式时会继承上层节点的font-size ([0fa62c2](https://github.com/wangeditor-team/wangEditor/commit/0fa62c2db525668f6b6555f8566de2cd8212042d))
* 同一节点只能绑定一个编辑器实例 ([935d03f](https://github.com/wangeditor-team/wangEditor/commit/935d03faed90cf5ee54aa5ab6b07a9776a655857))
* 修复表格最后2列会一起删除 ([f86aeba](https://github.com/wangeditor-team/wangEditor/commit/f86aebaa66ab5e5e09fcbcbf4859d42d3b765958))
* 修复排除自扩展菜单 ([66ebc43](https://github.com/wangeditor-team/wangEditor/commit/66ebc4357fe30ff05daa3c5de36c46431a66b0e8))
* 选中图片插入链接,页面卡死 ([df3f464](https://github.com/wangeditor-team/wangEditor/commit/df3f464791140e6fa34009528b895dea10cc41d4))
* 粘贴pdf内容卡死 ([f57674c](https://github.com/wangeditor-team/wangEditor/commit/f57674c440b59aca44ea749686505941c5462e97))
* 字体配置类型扩展 ([a0c30c1](https://github.com/wangeditor-team/wangEditor/commit/a0c30c13bbea8e376c10d93c59c7852bc0235f78))

## [4.6.15](https://github.com/wangeditor-team/wangEditor/compare/v4.6.14...v4.6.15) (2021-04-15)


### Bug Fixes

* 插入链接内容错误 ([d2be13a](https://github.com/wangeditor-team/wangEditor/commit/d2be13a210f24e2395acc7bf7636b8db5fe69c4b))
* 设置showLinkImg=false & uploadImgShowBase64=true 后，无法两次上传同一张图片 ([860fc52](https://github.com/wangeditor-team/wangEditor/commit/860fc5218e94829749599ba314392d817152fd17))
* 添加issue地址 ([90a5e45](https://github.com/wangeditor-team/wangEditor/commit/90a5e451c9f9b6f81207aaf61b6ad47b4c117ce8))
* 修复插入代码异常问题 ([b5fb553](https://github.com/wangeditor-team/wangEditor/commit/b5fb55350cd43715d210e4f78707aca83952106e))
* 修复从浏览器点击复制后图片粘贴到编辑器图片出现2个bug ([899748b](https://github.com/wangeditor-team/wangEditor/commit/899748b97f43aa883e536e7aab8d32e04e7578ab))
* 修复删除所有内容后无法触发blur的问题 ([1c5869f](https://github.com/wangeditor-team/wangEditor/commit/1c5869f27b4cd8d713bdb61f8d8788e356736d44))
* 修复长按删除键，失去焦点不触发blur的bug ([9530e28](https://github.com/wangeditor-team/wangEditor/commit/9530e282fc4deef7e7c1365262f8151b9cce3294))

## [4.6.14](https://github.com/wangeditor-team/wangEditor/compare/v4.6.13...v4.6.14) (2021-04-08)


### Bug Fixes

* 表格下面的标签无法删除 ([2f744b5](https://github.com/wangeditor-team/wangEditor/commit/2f744b5f72166752fdd5616485847cf088b947c1))
* 图片媒体库回调 ([bfc8484](https://github.com/wangeditor-team/wangEditor/commit/bfc84848a661d901a873b6992a7d8f88f62eac85))
* 修复鼠标选中文字之后，再选择文字中间位置插入图片导致选中文字消失的问题 ([eaeb816](https://github.com/wangeditor-team/wangEditor/commit/eaeb8169dd055c53b288125d4e062d9ae51157c9))


### Features

* 修复了append 时意外清除空格的问题 ([7b8273e](https://github.com/wangeditor-team/wangEditor/commit/7b8273eb60d184369698bb4f493b0b0b045f9a26))

## [4.6.13](https://github.com/wangeditor-team/wangEditor/compare/v4.6.12...v4.6.13) (2021-04-02)


### Bug Fixes

* 回车切换字号失效 ([5969bba](https://github.com/wangeditor-team/wangEditor/commit/5969bba505130f039343cb36789b08b0d157cbc8))
* 输入中文时，placeholder 不消失 ([a5d3d41](https://github.com/wangeditor-team/wangEditor/commit/a5d3d415bbc01b20b4a9f94d7945d431af22433d))
* 修复直接复制粘贴链接，不会触发自定义pasteTextHandle ([54d7d21](https://github.com/wangeditor-team/wangEditor/commit/54d7d21e66d423cebcd6c3b0d53f47b439da6ab7))
* 修改 togglePlaceholder 判断逻辑 ([34981fc](https://github.com/wangeditor-team/wangEditor/commit/34981fcf5b892a6c2e7de3bbd64842ef86b1557f))


### Features

* 视频新增对齐方式 ([706ce15](https://github.com/wangeditor-team/wangEditor/commit/706ce1513fb7269dc21e60dfd1ea9f63b0431288))

## [4.6.12](https://github.com/wangeditor-team/wangEditor/compare/v4.6.11...v4.6.12) (2021-03-26)

## [4.6.11](https://github.com/wangeditor-team/wangEditor/compare/v4.6.10...v4.6.11) (2021-03-25)


### Bug Fixes

* 兼容火狐浏览器video标签存在时光标无法输入问题 ([452a494](https://github.com/wangeditor-team/wangEditor/commit/452a4941dd6e961b90ec72b1f78d9502fe64403f))
* 文字颜色和背景颜色中的白色不容易辨别, 调整位置 ([6f0390b](https://github.com/wangeditor-team/wangEditor/commit/6f0390b44994bf4f026180ab10655317c45e6d57))
* 修复取消表头后，空行被删除 ([82e47df](https://github.com/wangeditor-team/wangEditor/commit/82e47dff61e094c87babeeda89ba00c17085252c))
* 修复设置isFocus为false的情况下，初始化完成后依旧触发blur事件的问题 ([96e7a1e](https://github.com/wangeditor-team/wangEditor/commit/96e7a1e56909a8695d670e925a0ce6010f123a46))
* 修复鼠标移出编辑区后获取选区异常 ([967e612](https://github.com/wangeditor-team/wangEditor/commit/967e612fc07013efcaf3699cee5afa8b173a2b40))
* 粘贴图片性能问题 ([45db80b](https://github.com/wangeditor-team/wangEditor/commit/45db80bf89fdd2786f2057606e5249fe0e5abd2e))
* wps粘贴问题 ([772d704](https://github.com/wangeditor-team/wangEditor/commit/772d704bfd5d4f4ebdad566711112e4a93663414))

## [4.6.10](https://github.com/wangeditor-team/wangEditor/compare/v4.6.9...v4.6.10) (2021-03-18)


### Bug Fixes

* 菜单tooptip上标下标自定义配置 ([3d98863](https://github.com/wangeditor-team/wangEditor/commit/3d988632c47fc63a6a88a776d09043ffa62a9f1c))
* 过滤 word 复制的无用标签 ([74ec31f](https://github.com/wangeditor-team/wangEditor/commit/74ec31fc25c25b8962e6f32ef4ae1922e0ca64be))
* 修复代码块作为最后一个元素时,用户无法输入的问题 ([c899d98](https://github.com/wangeditor-team/wangEditor/commit/c899d983a58b5924b14b5b9809956f9051feb558))
* 修复a标签样式重置 ([dafee8f](https://github.com/wangeditor-team/wangEditor/commit/dafee8fd79f67f73e0a0f1a718b80999b98477bd))

## [4.6.9](https://github.com/wangeditor-team/wangEditor/compare/v4.6.8...v4.6.9) (2021-03-11)


### Bug Fixes

* 复制图片-粘贴-走自定义上传逻辑 ([83b9297](https://github.com/wangeditor-team/wangEditor/commit/83b9297ebcdb54489ca0471084493d390ffd68e2))
* 将hover引起droplist菜单显示改为click ([b672e16](https://github.com/wangeditor-team/wangEditor/commit/b672e16a16b14166a757803b1ae255f46443d51f))
* 图片回车被删除的问题解决 ([3bf78b3](https://github.com/wangeditor-team/wangEditor/commit/3bf78b3ae2958c98ea4d72f686486e27e6439ba6))
* 修复内容为空时，菜单失效的问题 ([6c36f04](https://github.com/wangeditor-team/wangEditor/commit/6c36f04fe21e8926c9ab4242e02032823e1906c7))
* 修复上传图片格式的验证配置 ([dc24b75](https://github.com/wangeditor-team/wangEditor/commit/dc24b752798a37a65f99735be85702cbeadab916))
* 修复textSelector中带标签的内容不能带入编辑器内 ([4dbd023](https://github.com/wangeditor-team/wangEditor/commit/4dbd023fd53ed3f7cc285f622b4f9c56d3efeb65))
* 修改文件大小换算错误问题 ([42497b7](https://github.com/wangeditor-team/wangEditor/commit/42497b70839758e7d4b3d15fd6af5917296016bb))
* 在ie编辑器外部mouseup,选区保存错误 ([8aca308](https://github.com/wangeditor-team/wangEditor/commit/8aca3089b3bf77a9fa987e01d57e0b85a1143458))
* 增加默认表情 ([810bc90](https://github.com/wangeditor-team/wangEditor/commit/810bc90f2f6ead2f78c5861914202278b08a6bdd))

## [4.6.8](https://github.com/wangeditor-team/wangEditor/compare/v4.6.7...v4.6.8) (2021-03-04)


### Bug Fixes

* 菜单分离后禁止编辑器内容不显示 ([97fd71e](https://github.com/wangeditor-team/wangEditor/commit/97fd71e151a1df83d88e3c3446d367dc6c7c8286))
* 默认设置空行由<p><br></p>改成<p data-we-empty-p=""><br></p> ([68e29ba](https://github.com/wangeditor-team/wangEditor/commit/68e29ba16166f6ed83575d48c191af1f076e174e))
* 修复右键取消选区bug ([b80f396](https://github.com/wangeditor-team/wangEditor/commit/b80f396f60d03ba253ef7a29afc5a0cdff872bf1))
* 修复在不显示上传网络图片时，点击toolbar的上传图片按钮非icon区域无法打开文件选择的bug ([d8c3267](https://github.com/wangeditor-team/wangEditor/commit/d8c32678fe1dae25dbe91eef53e423263ad75ba9))
* 粘贴内容为段落的时候不再把光标移动到编辑区域末端 ([792ed51](https://github.com/wangeditor-team/wangEditor/commit/792ed51b91a352bf7f899bb27a7dde1c31e2b688))


### Performance Improvements

* 不传入config.onchange时，避免不必要的Text.html调用 ([f130083](https://github.com/wangeditor-team/wangEditor/commit/f1300837b19f62a9979b9ac35d81040c8056f84b))

## [4.6.7](https://github.com/wangeditor-team/wangEditor/compare/v4.6.6...v4.6.7) (2021-02-26)


### Bug Fixes

* 全屏的时候提示取消全屏 ([1f03358](https://github.com/wangeditor-team/wangEditor/commit/1f033581905896b657b5fa0b6d4e204a44eb45af))
* 为code标签添加类名，为标签内的语言类型。去除pre标签的type行内属性. ([b9c787d](https://github.com/wangeditor-team/wangEditor/commit/b9c787df68eee71fd2226bb79e8edc5248c0d7e8))
* 修复文字粘贴后光标会移至编辑器最底下的bug ([b4560fd](https://github.com/wangeditor-team/wangEditor/commit/b4560fd18c2ff0e5464412c38f29976d02cdcabf))

## [4.6.6](https://github.com/wangeditor-team/wangEditor/compare/v4.6.5...v4.6.6) (2021-02-04)


### Bug Fixes

* 保证table后面始终有dom ([b9f8ff0](https://github.com/wangeditor-team/wangEditor/commit/b9f8ff08ba9d398c7e1eeda7d9e1b5ea257a0265))
* 全屏编辑区域高度自适应 ([1c110e3](https://github.com/wangeditor-team/wangEditor/commit/1c110e33b7dc58fd0c4d37ff5eebebbc59ec3463))
* 失去焦点的时候判断 flag 是否为 失去焦点状态 如果是 则不重复触发 ([f302827](https://github.com/wangeditor-team/wangEditor/commit/f3028270d7cf1c6ae26521745fc9e8bf08ffe6af))
* 修复视频无法删除 ([33f39be](https://github.com/wangeditor-team/wangEditor/commit/33f39becbc1059f148c9d756549b6d24cad20acd))
* 修复图片上次icon生成不正确的错误 ([b261b0b](https://github.com/wangeditor-team/wangEditor/commit/b261b0b845f9ab1ac9b0140d2deae972803aaeff))
* 修复由于某些操作导致的控制台报undefined的错误 ([ded969d](https://github.com/wangeditor-team/wangEditor/commit/ded969d56b5f2a5aed833888e1c92362b4e1e014))
* 修复todo样式冲突问题 ([c829b9e](https://github.com/wangeditor-team/wangEditor/commit/c829b9ee4d7674c055b81cba7f89e959fe44c5ae))
* 修改 table 的测试用例 ([c366a5e](https://github.com/wangeditor-team/wangEditor/commit/c366a5ee3aedfce6049fc1acd3638ce437a8cfca))
* issue-2872 ([2cb50c5](https://github.com/wangeditor-team/wangEditor/commit/2cb50c5059a47968256c8119a2362b85477d76cc))
* txt.html()获取的内容中将没有自闭和的标签输出为自闭和标签 ([5769044](https://github.com/wangeditor-team/wangEditor/commit/57690447ed725c9eccab02497781725a7f6bee79))

## [4.6.5](https://github.com/wangeditor-team/wangEditor/compare/v4.6.4...v4.6.5) (2021-01-28)


### Bug Fixes

* 全选情况下不能正常覆盖内容 ([5faff48](https://github.com/wangeditor-team/wangEditor/commit/5faff48536abcb562154ad444cce96428148e9d5))
* 修复图片只有上传本地图片时提示文字不显示的问题 ([b8ae515](https://github.com/wangeditor-team/wangEditor/commit/b8ae515b9cf4e0da9fb4ca066512705e7b4cd558))
* 修复a标签设置颜色，按下回车后颜色值失效的问题 ([bbdf429](https://github.com/wangeditor-team/wangEditor/commit/bbdf4294bc579bf5efc1c7df1b357d81923c018c))
* table check ol and ul reach editor ([6b2325d](https://github.com/wangeditor-team/wangEditor/commit/6b2325d32498056c04c9ec09a2b881ebf3b5d9ed))
* table rowValue colValue is positive integer ([1599e15](https://github.com/wangeditor-team/wangEditor/commit/1599e1542ffa48d43a179b36c224cc1503648e2a))
* todo list remove error ([400fa34](https://github.com/wangeditor-team/wangEditor/commit/400fa341c6e23b43e98dd51a75c190c9786ebfce))


### Features

* 图片支持添加alt，超链接，当仅有上传图片功能时点击菜单直接弹出选择文件弹窗 ([6808c48](https://github.com/wangeditor-team/wangEditor/commit/6808c4865f6a6b5af58d109263c25760ef908808))

## [4.6.4](https://github.com/wangeditor-team/wangEditor/compare/v4.6.3...v4.6.4) (2021-01-21)


### Bug Fixes

* 处理最后一个元素为代码块时的跳出问题 ([023b86b](https://github.com/wangeditor-team/wangEditor/commit/023b86b23dc8a5160a5112f4c6c4da5c12b82b92))
* 工具栏tooltips开关配置 ([1c6d08c](https://github.com/wangeditor-team/wangEditor/commit/1c6d08c1c42f0668cdfe77f2f8250cdfe1631f2f))
* 修复选中多行文本时无法设置head的问题 ([89a49cc](https://github.com/wangeditor-team/wangEditor/commit/89a49ccbc1ab07eaeb8f5fc508296f9dd3f7b187))
* 优化粘贴 input 复制内容，多次粘贴会产生多余的html问题 ([f12d6ee](https://github.com/wangeditor-team/wangEditor/commit/f12d6ee6007f48fb4958f5538201468263f83757))
* 粘贴样式配置失效处理 ([332c079](https://github.com/wangeditor-team/wangEditor/commit/332c0799d1d427aab94fc21d0add62baacc4fc94))
* bar与text分离时,追加编辑器子节点 ([a6329f6](https://github.com/wangeditor-team/wangEditor/commit/a6329f66e59a1e2086186970d3958de930c79ddc))


### Features

*  扩展菜单注册太过繁琐 [#2493](https://github.com/wangeditor-team/wangEditor/issues/2493) ([9162b62](https://github.com/wangeditor-team/wangEditor/commit/9162b6283d5e82c84ac7c12bd8a6d5923ac58f54))

## [4.6.3](https://github.com/wangeditor-team/wangEditor/compare/v4.6.2...v4.6.3) (2021-01-14)


### Bug Fixes

* 标题按钮设置异常,错别字修复 ([37addbc](https://github.com/wangeditor-team/wangEditor/commit/37addbc081a7989bebe996c839ed78eb577758fc))
* 撤销和恢复按钮的tooltip描述不正确 ([af6fddc](https://github.com/wangeditor-team/wangEditor/commit/af6fddc374629fb1922cff106839aa0ddb2bbeee))
* 内容为空时append多出空行&createPanelConf 修改支持异步返回 ([bde84fd](https://github.com/wangeditor-team/wangEditor/commit/bde84fd6976fb77ee6174dd28d79b78970d7c1b9))
* 去掉在线图片和视频插入的校验限制 ([9e2f14a](https://github.com/wangeditor-team/wangEditor/commit/9e2f14ab1cc39bea43ea393ae4f5bfa75f57abbb))
* 失去焦点tooltip不消失 ([3c48acf](https://github.com/wangeditor-team/wangEditor/commit/3c48acfe41acb74bdce63f70d34358a953681dd0))
* 完善对齐 ([1b22c2a](https://github.com/wangeditor-team/wangEditor/commit/1b22c2a782ecef455db35ae172f2e6f95e248d5f))
* 修复撤销功能在某些极端情况下报错 ([578c3ce](https://github.com/wangeditor-team/wangEditor/commit/578c3ced5bda396b2d8c8a76dc95042b18cddbef))
* 修复初始化编辑器调用 API 插入内容，快捷键撤销报错的问题 ([3e2c375](https://github.com/wangeditor-team/wangEditor/commit/3e2c375275465e97544593e69dcc6e5982a382fb))
* 修复了列表对齐的问题 ([a4e43fd](https://github.com/wangeditor-team/wangEditor/commit/a4e43fd1f3111b1869c6c47895d221cc64dc7a9a))
* 修复eidtor.txt.append和editor.txt.html光标移动不正确问题 ([045782b](https://github.com/wangeditor-team/wangEditor/commit/045782be1138e197ee638a9b9b168d714b923de4))


### Features

* 新增上传视频功能 ([c9e5446](https://github.com/wangeditor-team/wangEditor/commit/c9e5446f08acf80487ba36adebe679a4c45adda6))

## [4.6.2](https://github.com/wangeditor-team/wangEditor/compare/v4.6.1...v4.6.2) (2021-01-07)


### Bug Fixes

* wps粘贴bug修复 ([735e878](https://github.com/wangeditor-team/wangEditor/commit/735e87861de20f6ef916a486e125a080cf13f18c))

## [4.6.1](https://github.com/wangeditor-team/wangEditor/compare/v4.6.0...v4.6.1) (2020-12-31)


### Bug Fixes

* 工具栏鼠标悬浮时显示提示文字 ([6bebfb4](https://github.com/wangeditor-team/wangEditor/commit/6bebfb48b20921298d5d3dddf1cbd3ac4e7a662f))
* 首次载入不获取焦点的情况菜单无效的问题 ([d89efe4](https://github.com/wangeditor-team/wangEditor/commit/d89efe41ca96a0f7b62646f351a73455bffb3216))

# [4.6.0](https://github.com/wangeditor-team/wangEditor/compare/v4.5.4...v4.6.0) (2020-12-24)


### Bug Fixes

* 初始化时对编辑区的click进行saveRange一次性绑定 ([4981cbd](https://github.com/wangeditor-team/wangEditor/commit/4981cbd2e7ba20e812dd2c6f9b1b7b3932b4a09f))
* 对齐menu 单元测试修改过测 ([f3ce71c](https://github.com/wangeditor-team/wangEditor/commit/f3ce71ce86dd47d43867f4753b96f1760db96752))
* 兼容ie11 ([694d3ad](https://github.com/wangeditor-team/wangEditor/commit/694d3adc0a4d18e243eb446247d8eff1c1f5a66c))
* 删除多余文件和代码，增加判空 ([3f15639](https://github.com/wangeditor-team/wangEditor/commit/3f156392a1e544e0b9e76650d5e3555c413de214))
* 修复对齐菜单bug ([b9242fa](https://github.com/wangeditor-team/wangEditor/commit/b9242fa7bbfe4b4fe722b4d40c980c843c1b0d1f))
* 修复设置focus为false,无法正常粘贴的问题 ([d551b26](https://github.com/wangeditor-team/wangEditor/commit/d551b267318b8a23571d2eac792b84b24053f97a))
* 修复元素指定id作为菜单 ([2d83fc2](https://github.com/wangeditor-team/wangEditor/commit/2d83fc2dd16cc3152ebc320b5e2d030c22e3e72e))
* 修复在styleWithCss下的产生的样式冲突 ([2460ef9](https://github.com/wangeditor-team/wangEditor/commit/2460ef9f41a6cfd99ba7226996a887b0a7a9f596))
* 修复chrome下在第一行设置todo无法删除的问题 ([b3db3b0](https://github.com/wangeditor-team/wangEditor/commit/b3db3b0723e4c1fa34f84b34124d6a2c8bd96fe1))
* 修改对齐方式单侧 ([a506544](https://github.com/wangeditor-team/wangEditor/commit/a5065441f37d5e4fe15fd5029716173585992a03))
* 修改函数名,增加可读性 ([0181bcc](https://github.com/wangeditor-team/wangEditor/commit/0181bcc632fe8fd8fcd8763cf368dfc6056c1a04))
* fix ie problem ([f840d90](https://github.com/wangeditor-team/wangEditor/commit/f840d90af9895b7134792d51a4b72d99b2cc0832))
* issue-2518 对齐修复 ([4bd6392](https://github.com/wangeditor-team/wangEditor/commit/4bd6392f7a320c69bfc85702a5b7133930e2b6f0))


### Features

* 完成todo功能 ([fcceeba](https://github.com/wangeditor-team/wangEditor/commit/fcceeba68c2af1da118327d1e190702f2d47dc65))

## [4.5.4](https://github.com/wangeditor-team/wangEditor/compare/v4.5.3...v4.5.4) (2020-12-18)


### Bug Fixes

* 插入网络视频的校验 ([285029d](https://github.com/wangeditor-team/wangEditor/commit/285029d1de5b0b488700810b9217ae84eff3791a))
* 设置完标题回车后,光标消失 ([0121c5c](https://github.com/wangeditor-team/wangEditor/commit/0121c5cdc7e74640228a217cd0510c98efaba5bc))
* 修改插入视频校验的正则 ([26196ac](https://github.com/wangeditor-team/wangEditor/commit/26196ac88467d6d83e1d93c34cdb84468ca440b0))
* 修改插入视频校验的正则 ([38e6289](https://github.com/wangeditor-team/wangEditor/commit/38e62898c83a4e074bae1764b381e496453bd401))
* 修改插入视频校验的正则 ([9944d51](https://github.com/wangeditor-team/wangEditor/commit/9944d515ebee27509395230be696abdc352dd43a))


### Features

* editor.config新增excludeMenus配置 ([5401280](https://github.com/wangeditor-team/wangEditor/commit/5401280ea9e54d920b3541078fd2f39955235a92))
* editor.config增加excludeMenus配置 ([15f1caa](https://github.com/wangeditor-team/wangEditor/commit/15f1caae024b79e76412c8d1aad48c9dac97122f))

## [4.5.3](https://github.com/wangeditor-team/wangEditor/compare/v4.5.2...v4.5.3) (2020-12-10)


### Bug Fixes

* [#2469](https://github.com/wangeditor-team/wangEditor/issues/2469) 火狐83版本多了一个诡异空格,81.0.2没有空格 ([b070e55](https://github.com/wangeditor-team/wangEditor/commit/b070e550ce8c5a52c874ddba021cc8f69c3e9b1b))
* 添加一个 向上查到的方法 限制在 编辑器内，修改表格的触发判断 ([c2aa192](https://github.com/wangeditor-team/wangEditor/commit/c2aa192a1a2b295be0ab7deabd15860a8b9be0eb))
* 修复标题样式会被覆盖的问题 ([864f3ba](https://github.com/wangeditor-team/wangEditor/commit/864f3baaaa1e619be7ad682a1dd9b82e69280883))
* 修改按钮的 type 为 button ([6037b91](https://github.com/wangeditor-team/wangEditor/commit/6037b913ce1aa7e1e4149262295da4d9c301935c))
* 粘贴文字保留空格 ([d935195](https://github.com/wangeditor-team/wangEditor/commit/d935195de4bda4c9fe0dd5b5dc793109b752c87a))
* 粘贴文字中间空格保留 ([7499d29](https://github.com/wangeditor-team/wangEditor/commit/7499d292fab5b4c7bb3b645dd9c811fb44d82c9f))
* 最后一行文本看不见 ([66c95a9](https://github.com/wangeditor-team/wangEditor/commit/66c95a9596a41c0a41da6fb5cb9ffa10a5975847))


### Features

* 插入网络视频的校验和回调 ([dbbbda2](https://github.com/wangeditor-team/wangEditor/commit/dbbbda2757237a6e55d618d51bdb8bf0f2b95e20))

## [4.5.2](https://github.com/wangeditor-team/wangEditor/compare/v4.5.1...v4.5.2) (2020-11-27)


### Bug Fixes

* 错误提示类型优化 ([61cc9b4](https://github.com/wangeditor-team/wangEditor/commit/61cc9b4d5081ce7e0733753e138ae2ff4f157921))
* 多实例全屏的问题 ([83f6b43](https://github.com/wangeditor-team/wangEditor/commit/83f6b43db87a4671d46d302f975a5e6bf7b8b070))
* 去掉测试全屏的代码 ([03a3f81](https://github.com/wangeditor-team/wangEditor/commit/03a3f811a01255bb5aeb8f6a64985919df76e271))
* 添加 custom alert 的 html 文档 ([baba963](https://github.com/wangeditor-team/wangEditor/commit/baba96388c819e8b51288fb8997d14998f3c7447))
* 添加居中样式 ([8db384a](https://github.com/wangeditor-team/wangEditor/commit/8db384ab19987943e255b22fe77144c0be9ebf8a))
* fix wrap wrap in firefox ([7ebdbbf](https://github.com/wangeditor-team/wangEditor/commit/7ebdbbf3dbccf5a83d02518698d74ef643a1576b))
* fix: 某些情况下，无法成功粘贴 ([dbfe2eb](https://github.com/wangeditor-team/wangEditor/commit/dbfe2eb3db0426a22fe3296fa3e62c1dc44b6537)), closes [#2530](https://github.com/wangeditor-team/wangEditor/issues/2530) [#2530](https://github.com/wangeditor-team/wangEditor/issues/2530)
* img 添加 重置 效果 ([10df1bb](https://github.com/wangeditor-team/wangEditor/commit/10df1bbcda00b723299a4935077a3636f4a09906))

## [4.5.1](https://github.com/wangeditor-team/wangEditor/compare/v4.5.0...v4.5.1) (2020-11-26)


### Bug Fixes

* setJSON的表格不成功的问题解决 ([f57395b](https://github.com/wangeditor-team/wangEditor/commit/f57395b3445fe05debdeaf4eaae7ddd1ce44da1e))
* uploadImgAccept 类型 ([18b7a42](https://github.com/wangeditor-team/wangEditor/commit/18b7a42e02a6079502d3ce7583524f3f391a082f))
* 去掉console.log ([6197747](https://github.com/wangeditor-team/wangEditor/commit/6197747700ce99616831624688f6395b4baaae9b))
* 变量名优化 ([5d20096](https://github.com/wangeditor-team/wangEditor/commit/5d20096319a63c11bd9071dfe107245fac632597))
* 完善了设置字体大小、样式、背景、文字颜色等菜单的功能 ([3072543](https://github.com/wangeditor-team/wangEditor/commit/3072543efdff2cb36f594ac396eb6c2c61815d13))
* 添加自定义setJSON表格按钮 ([7bd76c6](https://github.com/wangeditor-team/wangEditor/commit/7bd76c6ebab4011e40fab4d78fa59c74903df7b6))


### Features

* 🎸 support custom accept for image [#1655](https://github.com/wangeditor-team/wangEditor/issues/1655) ([5af4dcd](https://github.com/wangeditor-team/wangEditor/commit/5af4dcd505a41a3f4fbe6b1e885c0005bcf887d8))

# [4.6.0](https://github.com/wangeditor-team/wangEditor/compare/v4.5.0...v4.6.0) (2020-11-25)


### Bug Fixes

* setJSON的表格不成功的问题解决 ([f57395b](https://github.com/wangeditor-team/wangEditor/commit/f57395b3445fe05debdeaf4eaae7ddd1ce44da1e))
* uploadImgAccept 类型 ([18b7a42](https://github.com/wangeditor-team/wangEditor/commit/18b7a42e02a6079502d3ce7583524f3f391a082f))
* 去掉console.log ([6197747](https://github.com/wangeditor-team/wangEditor/commit/6197747700ce99616831624688f6395b4baaae9b))
* 变量名优化 ([5d20096](https://github.com/wangeditor-team/wangEditor/commit/5d20096319a63c11bd9071dfe107245fac632597))
* 完善了设置字体大小、样式、背景、文字颜色等菜单的功能 ([3072543](https://github.com/wangeditor-team/wangEditor/commit/3072543efdff2cb36f594ac396eb6c2c61815d13))
* 添加自定义setJSON表格按钮 ([7bd76c6](https://github.com/wangeditor-team/wangEditor/commit/7bd76c6ebab4011e40fab4d78fa59c74903df7b6))


### Features

* 🎸 support custom accept for image [#1655](https://github.com/wangeditor-team/wangEditor/issues/1655) ([5af4dcd](https://github.com/wangeditor-team/wangEditor/commit/5af4dcd505a41a3f4fbe6b1e885c0005bcf887d8))

# [4.5.0](https://github.com/wangeditor-team/wangEditor/compare/v4.4.2...v4.5.0) (2020-11-20)


### Bug Fixes

* console去掉 ([d49f3d3](https://github.com/wangeditor-team/wangEditor/commit/d49f3d3c0ca3f17fe848390c9965270220ead9f7))
* fileName 去掉随机数 ([d7a3a34](https://github.com/wangeditor-team/wangEditor/commit/d7a3a34a3832135b07f2c383de6011b5829a875b))
* online改成catalog ([ba43299](https://github.com/wangeditor-team/wangEditor/commit/ba4329990f2fd58ffb062678496997c52c9b9b2d))
* server rm shell ([565ddb3](https://github.com/wangeditor-team/wangEditor/commit/565ddb3f345cf8e0900e7fa71809785193235928))
* 优化运行机制 ([900f7dd](https://github.com/wangeditor-team/wangEditor/commit/900f7dd17d69973469bcaae8429c521c43896045))
* 引用路径 ([573fd4a](https://github.com/wangeditor-team/wangEditor/commit/573fd4a48306ea4d5f39b56f84a93d967057a15a))
* 新增API滚动到指定标题 ([3772ff0](https://github.com/wangeditor-team/wangEditor/commit/3772ff08b1d5e853cd7a6588fde487ff26ae0449))
* 类型引用位置问题 ([3922c8d](https://github.com/wangeditor-team/wangEditor/commit/3922c8d06286b15de643956d924dd6dcf1e3c827))


### Features

* 添加大纲配置及demo ([99b12f9](https://github.com/wangeditor-team/wangEditor/commit/99b12f9c0b1ade3b2810828b8b5c35e65dcebd43))

## [4.4.2](https://github.com/wangeditor-team/wangEditor/compare/v4.4.1...v4.4.2) (2020-11-19)


### Bug Fixes

* [#2336](https://github.com/wangeditor-team/wangEditor/issues/2336) 重复截图粘贴到编辑区内,只会粘贴出第一次截图的图片 ([627c485](https://github.com/wangeditor-team/wangEditor/commit/627c4853e9b4e612084eaf9703c5c21cf65546fc))
* [#2469](https://github.com/wangeditor-team/wangEditor/issues/2469) 火狐浏览器经过多次删除内容，重新添加内容 文章内容没有被p标签包裹 ([6c6e879](https://github.com/wangeditor-team/wangEditor/commit/6c6e879d5a0722634115c237fedc5e7e6d1a2519))
* eslint 问题 ([5e918d6](https://github.com/wangeditor-team/wangEditor/commit/5e918d6850267f85c42903fbe494ace8e9586bb5))
* 引用路径 ([01dc0ef](https://github.com/wangeditor-team/wangEditor/commit/01dc0ef0a1d4e613fd308a85472764edf5b54818))
* 火狐中 getSelectionRangeTopNodes返回值 可能为undefined ([25a544a](https://github.com/wangeditor-team/wangEditor/commit/25a544a2e58de521f63e406aedd8493a36b41d98))
* 直接按回车placeholder没有隐藏,只有回车2次以上才能隐藏 ([baa47ca](https://github.com/wangeditor-team/wangEditor/commit/baa47ca2c5a1064054490a829b8ed34ffd8c40f0))

## [4.4.1](https://github.com/wangeditor-team/wangEditor/compare/v4.4.0...v4.4.1) (2020-11-18)


### Bug Fixes

* [#2301](https://github.com/wangeditor-team/wangEditor/issues/2301) zone.js Promise冲突问题 ([0f63a04](https://github.com/wangeditor-team/wangEditor/commit/0f63a0466f98ad9a183afbd3cb85160b1eb4b6a9))
* [#2398](https://github.com/wangeditor-team/wangEditor/issues/2398) 自动聚焦 不触发 onfocus & 直接失焦不触发 onblur ([e3b39c6](https://github.com/wangeditor-team/wangEditor/commit/e3b39c6643cc2ab47909f692faad6a9c7c68e483))
* 修复引用按钮高亮失效的问题 ([0e4a30e](https://github.com/wangeditor-team/wangEditor/commit/0e4a30e02cc02f54107c49597308d9665865c337))
* 去除多余变量 ([e0f900d](https://github.com/wangeditor-team/wangEditor/commit/e0f900da4e465a061d1de48c85a7bbf782c9737d))

# [4.4.0](https://github.com/wangeditor-team/wangEditor/compare/v4.3.0...v4.4.0) (2020-11-17)


### Bug Fixes

* 修复DomElement内部处理错误 ([04484e0](https://github.com/wangeditor-team/wangEditor/commit/04484e086194fddcdec14abb924a2c963f482869))
* 修复firefox下会自动换行的问题 ([490c2e4](https://github.com/wangeditor-team/wangEditor/commit/490c2e48d2a0967a624f5475ce182fc2b12ed25f))
* 修复firefox和chrome下引用表现不一致的问题 ([6c33e7f](https://github.com/wangeditor-team/wangEditor/commit/6c33e7fb8d82bfde9130541c405c55d93d043a0a))
* 修复qoute下多行下表现态不一致的问题 ([cdbd853](https://github.com/wangeditor-team/wangEditor/commit/cdbd853ae74119cdab7f2574635ece17fd63c9ac))
* 修复单元测试不一致的地方 ([5bf49a2](https://github.com/wangeditor-team/wangEditor/commit/5bf49a2a92dacdaa1072ae1218fad52ce8ca4932))
* 修复多行和序列无法正常使用quote的问题 ([886072d](https://github.com/wangeditor-team/wangEditor/commit/886072d71b0405f9ae5183e2a767035a495f22e4))
* 修复空行引用回车的的问题 ([d5124c8](https://github.com/wangeditor-team/wangEditor/commit/d5124c8f3b307e8e4630318b33af7e421acd7043))
* 修复空行引用回车的的问题 ([6a68a83](https://github.com/wangeditor-team/wangEditor/commit/6a68a83cc4cac7e2bb27671b69386ab0bc35165a))
* 修复隐患 ([b6ab6e0](https://github.com/wangeditor-team/wangEditor/commit/b6ab6e0d6c63256e9a60926d30f9a7e6809cc343))
* 完善cypress配置 ([76b9920](https://github.com/wangeditor-team/wangEditor/commit/76b9920e28a26f4dbbf2f33fb501d65c5be745ac))
* 完善测试用例 ([f7d616b](https://github.com/wangeditor-team/wangEditor/commit/f7d616b4ec31b864c29a63e65faa2c17436bd822))
* 移除没有必要的依�� ([1e759e9](https://github.com/wangeditor-team/wangEditor/commit/1e759e96579e089529822b938cbbe7ffa4f3ec16))


### Features

* 使入口函数提示类型 ([ed60d29](https://github.com/wangeditor-team/wangEditor/commit/ed60d299cf487ae5c0cb2bc244773b06c216c76d))
* 修改容易更改的类型 ([62455b2](https://github.com/wangeditor-team/wangEditor/commit/62455b2681a3dddde20b43731e0cbd35dfabf796))
* 完善了测试用例 ([a126c01](https://github.com/wangeditor-team/wangEditor/commit/a126c01c463424fccb5ace7db0665fe1614b676b))
* 完善了编辑器菜单所有测试用例 ([d9a9b37](https://github.com/wangeditor-team/wangEditor/commit/d9a9b379c614796bf10ca94f80bb5ef7390706f2))
* 完善单元测试 ([4a724f9](https://github.com/wangeditor-team/wangEditor/commit/4a724f98bb8ad2e529bf770b42ec54d10886c0ce))
* 添加所以菜单的测试文件 ([d85f19a](https://github.com/wangeditor-team/wangEditor/commit/d85f19a097070cc803efaaf2363d644264737fd6))
* 添加新的测试 ([3a1ec8b](https://github.com/wangeditor-team/wangEditor/commit/3a1ec8b11ac07018239ca7e4ab1ac3941b7ac25f))
* 添加新的测试用例 ([af512d7](https://github.com/wangeditor-team/wangEditor/commit/af512d749ba6566a39ba69ccb32ba3db99bae64b))

# [4.3.0](https://github.com/wangeditor-team/wangEditor/compare/v4.2.2...v4.3.0) (2020-11-09)


### Bug Fixes

* getJSON example page ([1797c54](https://github.com/wangeditor-team/wangEditor/commit/1797c54a5efed4998581ce609f1a50dc8085dac0))
* 多实例 disable 异常 ([683fe00](https://github.com/wangeditor-team/wangEditor/commit/683fe00d86c8ba4020ebd35cd358960bc49dc6c2))


### Features

* 完成setJSON ([2982304](https://github.com/wangeditor-team/wangEditor/commit/298230449f15276975506fe1b54ee1abdad78e3d))

## [4.2.2](https://github.com/wangeditor-team/wangEditor/compare/v4.2.1...v4.2.2) (2020-11-05)


### Bug Fixes

* dev deploy build ([76eacbe](https://github.com/wangeditor-team/wangEditor/commit/76eacbeb9c55fd68a452ba90a0b9abeb0c11402d))
* dev 部署 ([0a403c4](https://github.com/wangeditor-team/wangEditor/commit/0a403c4401584b3f12effe04ff2270b80ab3e44a))
* npm i ([b498172](https://github.com/wangeditor-team/wangEditor/commit/b498172304668a569abce3f2b048f545c397cf91))
* pm2 start ([23130d6](https://github.com/wangeditor-team/wangEditor/commit/23130d6c90b4761d48ec5519e20c072c4e9d2d57))
* yml 语法 ([d6d8df8](https://github.com/wangeditor-team/wangEditor/commit/d6d8df8ce451321140c9d6b5ae27309b48925001))
* 修复quote在火狐浏览器下的问题 ([3166084](https://github.com/wangeditor-team/wangEditor/commit/31660846d9715e47d8a62de50a5bcae02da961aa))
* 修复quote某些情况下无法取消的问题 ([d8484e7](https://github.com/wangeditor-team/wangEditor/commit/d8484e7f5927312adbbd08744006921f5e34f1ae))
* 修复取消引用多出br的问题 ([c12139c](https://github.com/wangeditor-team/wangEditor/commit/c12139cbff123fa3aa8b8662971748ec4c316316))
* 修复多行下使用饮用的问题 ([5e3c972](https://github.com/wangeditor-team/wangEditor/commit/5e3c9727db66bbd9b5be4808cdc527d7f16577be))

## [4.2.1](https://github.com/wangeditor-team/wangEditor/compare/v4.2.0...v4.2.1) (2020-11-03)

# [4.2.0](https://github.com/wangeditor-team/wangEditor/compare/v4.1.0...v4.2.0) (2020-11-02)


### Bug Fixes

* release 配置 ([f07bedb](https://github.com/wangeditor-team/wangEditor/commit/f07bedb3a29fe1a9a15fa42d1b9fcb5ee49cf129))
* 修复上传本地图片覆盖问题 ([a5e87a2](https://github.com/wangeditor-team/wangEditor/commit/a5e87a239202b12df143b3c93070f03bf4acd947))
* 修复部分情况保存range错误,从而造成上传图片被替换或者复制粘贴失效的情况 ([35492c7](https://github.com/wangeditor-team/wangEditor/commit/35492c7e8b28522eafc5822c7d71abeb2db407f7))
* 修改描述 ([7bebdd9](https://github.com/wangeditor-team/wangEditor/commit/7bebdd98c0fcf073873298fbb079ece56adc8290))


### Features

* 添加问题模板 ([fca8ea1](https://github.com/wangeditor-team/wangEditor/commit/fca8ea11873f3e479810fa763a327af9d27f7edd))

# [4.1.0](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-10-29)


### Bug Fixes

* 优化代码 ([a0aaa78](https://github.com/wangeditor-team/wangEditor/commit/a0aaa78f356469919b458ab81cbef0a24bedadfc))
* 修复表格「删除行」中文翻译错误 ([d9e4ef4](https://github.com/wangeditor-team/wangEditor/commit/d9e4ef428617d56eba9c5a27d25aacf55a322bc0))
* 修复重复创建幕布，切换抖动 ([e3ff913](https://github.com/wangeditor-team/wangEditor/commit/e3ff913fffd420a3cb907f515e207b39ef3d5e1f))
* 修改html文件名称 ([9d76d90](https://github.com/wangeditor-team/wangEditor/commit/9d76d90bfe0a10500e3ab41553baf9db8210b56c))
* 修改名称、优化代码 ([cf5307a](https://github.com/wangeditor-team/wangEditor/commit/cf5307a3ba1855f0c39b6599a27b715a1070c0ca))
* 修改文件名称、css样式名称 ([d18631e](https://github.com/wangeditor-team/wangEditor/commit/d18631ea3a93cb49f2bacfa451598e8fa023d0eb))
* 删除console ([d46d2fc](https://github.com/wangeditor-team/wangEditor/commit/d46d2fc9a6f616b7250f734659990614893aa43c))
* 增加判断幕布isCurtain ([3c8b8cd](https://github.com/wangeditor-team/wangEditor/commit/3c8b8cd2aeafd6f68b9193687443ec3e5eebab7a))
* 禁用后去掉图片的 hover 样式 ([0e4420a](https://github.com/wangeditor-team/wangEditor/commit/0e4420a275518a58a97149578be74b5c729b3336))
* 移除没有必要的hooks ([21584e6](https://github.com/wangeditor-team/wangEditor/commit/21584e642f70512733d775b0ac20343a2802ef9c))


### Features

* 使用release-it优化了release工作流 ([6eb374b](https://github.com/wangeditor-team/wangEditor/commit/6eb374b3c3655f2591f4305f150eadb45004ae44))
* 新增幕布禁用api功能 ([e9b90f8](https://github.com/wangeditor-team/wangEditor/commit/e9b90f80771a9001e18264f1d0797fc36d99b974))
* 新增幕布禁用api功能 ([853e35a](https://github.com/wangeditor-team/wangEditor/commit/853e35a32c600fdc64ab7245789b0c6d4d8a2ff1))
* 新增幕布禁用api功能 ([7eec7b9](https://github.com/wangeditor-team/wangEditor/commit/7eec7b9904d479393b08a05682ac9dead4dfa520))
* 新增幕布禁用api功能 ([b2e7460](https://github.com/wangeditor-team/wangEditor/commit/b2e746087767dc55591f65ab5ed02adbfc6c39b5))



## [4.0.8](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-10-28)



## [4.0.7](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-10-28)


### Bug Fixes

* 修复粘贴多出空行问题 ([5620e1e](https://github.com/wangeditor-team/wangEditor/commit/5620e1ee48f8b132a62af444eda8ee30f4d24ff8))
* 修复粘贴文本不能换行的问题 ([163d98b](https://github.com/wangeditor-team/wangEditor/commit/163d98bdf64fd813d94b218c6cfe85c681204d06))


### Features

* 上传至ossDemo ([8852969](https://github.com/wangeditor-team/wangEditor/commit/8852969d386a5769ec8aa1004e2f8b78e4a09b66))
* 添加了commitlint校验commit msg规范 ([2bd41af](https://github.com/wangeditor-team/wangEditor/commit/2bd41af8d9973d396f0af1fad732a7e182746dbf))
* 缩进功能支持config选项indentation，用来指定缩进量 ([6367358](https://github.com/wangeditor-team/wangEditor/commit/6367358c4f287487a200a41f70917f5c7f99c42c))



## [4.0.6](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-10-25)


### Bug Fixes

* 删除 E.menuConstructor 的一些修补 ([3c22a59](https://github.com/wangeditor-team/wangEditor/commit/3c22a5964f8d2bee3cbcf1dd40c79236b37de67d))
* 外部无法获取 menuConstructors 中类的类型 [#2310](https://github.com/wangeditor-team/wangEditor/issues/2310) ([3354f7b](https://github.com/wangeditor-team/wangEditor/commit/3354f7b7e538be38a4327c953208f0ddd9ee50bb))
* 恢复扩展类在全局导出下CDN时不能使用问题 ([0e3ef92](https://github.com/wangeditor-team/wangEditor/commit/0e3ef92ebf17aaff5fa1a45ca0c6208ca91368b8))


### Features

* 切换基础类的导出方式和导出所需的类型 ([7884eb4](https://github.com/wangeditor-team/wangEditor/commit/7884eb4aff0760d92f99d2a18fec3417ede39916))



## [4.0.5](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-10-21)



## [4.0.4](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-10-19)


### Bug Fixes

* removeAttr ([738bd21](https://github.com/wangeditor-team/wangEditor/commit/738bd21c4f3b2fae51ac5fb70199de245c1e2493))
* 修复表格「删除行」中文翻译错误 ([fc0cf17](https://github.com/wangeditor-team/wangEditor/commit/fc0cf17e4ff5e3358ba0aa97804243bf21faf7ad))
* 修改设置百分比行为为width ([51be37f](https://github.com/wangeditor-team/wangEditor/commit/51be37f928971177bb880c02ca09132da46a5ba2))
* 先确保行为一致性 ([fab394b](https://github.com/wangeditor-team/wangEditor/commit/fab394bdc75a347e046f3fe5e5b18c53d90862fe))
* 设置图片百分比后还原图片height ([c99c03b](https://github.com/wangeditor-team/wangEditor/commit/c99c03bc3384a39485e7adf7814b8c4710599308))


### Features

* percentage ([f94a5df](https://github.com/wangeditor-team/wangEditor/commit/f94a5dfaa7543c8b309a8a2c89076d87769b6f53))
* server 输出url地址 package 添加 run start ([7127757](https://github.com/wangeditor-team/wangEditor/commit/71277572411b7c4fe6b5a766bbf553334d916131))
* 添加 removeAttr ([61edbb5](https://github.com/wangeditor-team/wangEditor/commit/61edbb5c64bccaa66a480c5ba0cf84848a7df3c7))



## [4.0.3](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-10-10)


### Bug Fixes

* 修复粘贴图片总是到最后一个实例中 [#2266](https://github.com/wangeditor-team/wangEditor/issues/2266) ([98ab9d3](https://github.com/wangeditor-team/wangEditor/commit/98ab9d3effb42c0a3a49d493bc9c47f73c86901d))



## [4.0.2](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-10-10)



## [4.0.1](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-10-10)



# [4.0.0](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-10-07)



## [0.1.2](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-09-29)


### Bug Fixes

* clone config ([d44650c](https://github.com/wangeditor-team/wangEditor/commit/d44650c994c1c68b0c05c3a1486dffbec7ed7cce))
* defaultConfig export ([73b7c12](https://github.com/wangeditor-team/wangEditor/commit/73b7c12076b0daddc20cafa51f8aa04ed4caf73a))
* deug ([d2da5c1](https://github.com/wangeditor-team/wangEditor/commit/d2da5c13d8eab4e602ba1d1c427ab31a07ba53d4))
* remove clone fn ([981da8a](https://github.com/wangeditor-team/wangEditor/commit/981da8a84eaa0b65bc1e6f0ffd11137504b7d49c))
* 修改变量 ([667712d](https://github.com/wangeditor-team/wangEditor/commit/667712d7a1f33be27bccd6027fdb66c346e5585d))
* 修改配置项返回方法 ([364bf78](https://github.com/wangeditor-team/wangEditor/commit/364bf78ae881a10b1c9dea5e3d8599845199f814))
* 全屏高度被覆盖问题 ([b4c68b6](https://github.com/wangeditor-team/wangEditor/commit/b4c68b6bcd32f7612d79494eaa792719ae4d88e9))
* 添加注释 ([c2332b6](https://github.com/wangeditor-team/wangEditor/commit/c2332b6f70eeeadcd7e4becf1e08d66b68339dab))


### Features

* 修改 ui 样式 ([79d9146](https://github.com/wangeditor-team/wangEditor/commit/79d91464571bcd9d38d97e1d5078a4eff0c3ee57))



## [0.1.1](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-09-24)


### Bug Fixes

* git revert ([651c68d](https://github.com/wangeditor-team/wangEditor/commit/651c68d3f27f3e1216178c93817a4ce67de2b076))
* showFullScreen ([0143db3](https://github.com/wangeditor-team/wangEditor/commit/0143db3e25a6a090b01936cdb0cd76b4b151f86e))
* 回调active的protected ([374c71f](https://github.com/wangeditor-team/wangEditor/commit/374c71ff6b064b5fc33ef2d1b33b507d5f9bb571))
* 暴露menu的active和unActive ([8c83ef7](https://github.com/wangeditor-team/wangEditor/commit/8c83ef7587614acc06cc60fe74092cf4b309b29c))
* 添加分离不生成全屏功能注释 ([b8903dd](https://github.com/wangeditor-team/wangEditor/commit/b8903dd0f9a98ff143710659b69a63f6dda509f9))
* 粘贴 span ([0e511d1](https://github.com/wangeditor-team/wangEditor/commit/0e511d1a34424a9069f3bc19433e3662a7c3196c))
* 调整全屏功能 ([c0b826e](https://github.com/wangeditor-team/wangEditor/commit/c0b826e9fcd046090830a58033dddfdae961367d))


### Features

* 全屏菜单 ([d2d8798](https://github.com/wangeditor-team/wangEditor/commit/d2d8798766b59e7b0bb609eb7298adbcbf5f1bf1))


### Reverts

* Revert "fix:处理enter的bug" ([a8544ef](https://github.com/wangeditor-team/wangEditor/commit/a8544ef638919de67a5ace3535a8a7a6d3098753))
* Revert "添加chexkbox图标" ([8c61346](https://github.com/wangeditor-team/wangEditor/commit/8c61346f2db739f33a28a924504456a310b0756d))



# [0.1.0](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-09-17)


### Bug Fixes

*  txt.html()过滤不必要的br ([a7d76e7](https://github.com/wangeditor-team/wangEditor/commit/a7d76e79759db4e6f9d1e8825244950bfa53dc08))
* config focus ([831effe](https://github.com/wangeditor-team/wangEditor/commit/831effef186605c9740aba3664e2a8bd8c6ef061))
* config focus ([58073ee](https://github.com/wangeditor-team/wangEditor/commit/58073eeac8019e393438d857ce754e9ca9c650f4))
* drop-list text left ([83ef607](https://github.com/wangeditor-team/wangEditor/commit/83ef607c8bbfd5b75b0cd01b2fb4ca95164b3b20))
* editor.txt.html('') 无效 ([aa4798c](https://github.com/wangeditor-team/wangEditor/commit/aa4798c45941fe48f1afa1edb41286bcad381984))
* IE11拖拽图片使用自带默认的 ([1a17174](https://github.com/wangeditor-team/wangEditor/commit/1a171747ec52da2d0a2e12b6abf22402e5029ca7))
* if case ([84139d1](https://github.com/wangeditor-team/wangEditor/commit/84139d1a56a8efb644c961a6f64d17c7332cc685))
* 上传图片 i18next 修改 ([8eb45c8](https://github.com/wangeditor-team/wangEditor/commit/8eb45c8b6406b7e23a22487e6cbba9aa19741c4c))
* 修改 i18next 初始化报错提醒 ([8eb71d9](https://github.com/wangeditor-team/wangEditor/commit/8eb71d948bda58ffaddea175c4b24cfc194c4441))
* 添加全屏icon ([cea53a7](https://github.com/wangeditor-team/wangEditor/commit/cea53a7ffcf5f0048e2d30f966b1369be37ed48c))
* 相同方法抽离 ([59347b9](https://github.com/wangeditor-team/wangEditor/commit/59347b9df099acf2eb33fa65f67db39430e654a2))
* 非中文情况下 带 icon 的下拉菜单 居左 ([367d42c](https://github.com/wangeditor-team/wangEditor/commit/367d42ccc346fd8652afcbc01cc05037fa422872))
* 非中文情况下带 icon 的下拉菜单居左 ([2cc206e](https://github.com/wangeditor-team/wangEditor/commit/2cc206e41b45808621c63fd646ab194b4b341594))


### Features

* auto focus & test & html ([6d9fe98](https://github.com/wangeditor-team/wangEditor/commit/6d9fe988cb4855fd310e3066c850bdff3c025a65))
* 分割线功能 ([930395b](https://github.com/wangeditor-team/wangEditor/commit/930395bad54394b5e63f4ab0593d848aa8894996))



## [0.0.28](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-09-10)


### Bug Fixes

* IE11 Promise ([402a755](https://github.com/wangeditor-team/wangEditor/commit/402a75504e8369fedb658078488d3e33c8adef07))
* 图片上传 - IE11 上传多张只显示一张 ([ffd663f](https://github.com/wangeditor-team/wangEditor/commit/ffd663f2de0a4f890cd1e1791395edf17b86ce7b))
* 图片上传 - 当限制一次最多是1张图片时，还可以ctrl多选 ([ad5f644](https://github.com/wangeditor-team/wangEditor/commit/ad5f644c589f38184546831da85d9e07bea6ebd2))
* 粘贴时丢失内容 ([d159844](https://github.com/wangeditor-team/wangEditor/commit/d159844a06a4f25ad644d7f1a5538b91c88c44d2))
* 隐藏 panel 的 bug ([8d1d6e6](https://github.com/wangeditor-team/wangEditor/commit/8d1d6e6570300069ba03cdc71b8d8847971db802))


### Features

* 图片点击选中事件 ([48c2574](https://github.com/wangeditor-team/wangEditor/commit/48c2574b2ace93d3db2c394814937553c50f578b))



## [0.0.27](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-09-04)


### Bug Fixes

* IE11 中兼容 quote 和 table ([77c5c23](https://github.com/wangeditor-team/wangEditor/commit/77c5c238566c4712aa302197a9d9de6c694e35d9))
* matches polyfill ([f21d33b](https://github.com/wangeditor-team/wangEditor/commit/f21d33bd0c6e9516464929043bdf9ba5263fbbd2))
* windows 上传图片错误 ([158e896](https://github.com/wangeditor-team/wangEditor/commit/158e896506b8b1673c9d199cbef9b77276b322b5))
* windows 上传文件 ([33efb38](https://github.com/wangeditor-team/wangEditor/commit/33efb38926d8f6c14f1ef456907f4e1b2ee42649))



## [0.0.26](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-09-03)


### Bug Fixes

* add lang ([0949679](https://github.com/wangeditor-team/wangEditor/commit/09496798b7c300b85bf87fcf65122aa47970fd7c))
* code & table add i18next ([4d7cd17](https://github.com/wangeditor-team/wangEditor/commit/4d7cd1786e6ac53bf925274e23df711834b96c1e))
* code i18n ([e71689e](https://github.com/wangeditor-team/wangEditor/commit/e71689e3e2d39e32efae1eb2680b0f414998e740))
* code lang edit ([2bfc17b](https://github.com/wangeditor-team/wangEditor/commit/2bfc17bf98460eb0e18985ac77955b5d73a0fe2e))
* i18Prefix -> i18nPrefix ([26609ce](https://github.com/wangeditor-team/wangEditor/commit/26609cefc18566be8a898c413b12242b38e4fb15))
* table tooltip i18n ([627bfbd](https://github.com/wangeditor-team/wangEditor/commit/627bfbd85c6f17527c4a57f8eb72564ec6c2758e))
* 修改翻译 ([f12a8b2](https://github.com/wangeditor-team/wangEditor/commit/f12a8b245dfddd273fca5fb2df1c3a0a01ea14e2))
* 修改词典 ([98eb8b8](https://github.com/wangeditor-team/wangEditor/commit/98eb8b84d69a52d567ebd090f1965796a6eef314))
* 修改词典 ([8ba1a55](https://github.com/wangeditor-team/wangEditor/commit/8ba1a55ea8057f9622e1f10ecc9ae4f363792375))
* 词典修改 ([f6b24e0](https://github.com/wangeditor-team/wangEditor/commit/f6b24e0a868fc1619aee17cd914c9d89f00a4f6a))



## [0.0.25](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-09-01)


### Bug Fixes

* 修复拖拽没触发change事件 ([b8fbb26](https://github.com/wangeditor-team/wangEditor/commit/b8fbb2628b8639d21cbd840f0ae8e1398aaf22db))
* 图片居中时，tooltip 和拖拽框不隐藏 ([f96546d](https://github.com/wangeditor-team/wangEditor/commit/f96546d5cb1b34959b95c80457eeb69fd78ab3d4))



## [0.0.24](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-08-24)


### Bug Fixes

* 解决了连删至上一行无效的问题 ([d5ed9df](https://github.com/wangeditor-team/wangEditor/commit/d5ed9df97d99e41b76a62e7544133d8930432712))
* 还原example index ([08c0e6c](https://github.com/wangeditor-team/wangEditor/commit/08c0e6c2b2a44de67f0465a2e08b1205fd2b992f))



## [0.0.23](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-08-23)


### Bug Fixes

* fontSize test error ([c6d5eee](https://github.com/wangeditor-team/wangEditor/commit/c6d5eee74d73eb48d8c37c402402d49538b53036))
* tooltip 元素过高时 不会跑出编辑范围 ([ed5efe4](https://github.com/wangeditor-team/wangEditor/commit/ed5efe470b2f377a692154fced2f10227483b5c2))
* top 算法修改 ([ff1735e](https://github.com/wangeditor-team/wangEditor/commit/ff1735e3c4c5ab9349ceb8d8ff26be3ec324d0d7))
* txt.append() 传入普通字符串报错 ([c65ef52](https://github.com/wangeditor-team/wangEditor/commit/c65ef5281affdf740ae69434853fd786cb9ee0aa))
* 中文输入法切换到英文，无法触发 onchange ([09b3c85](https://github.com/wangeditor-team/wangEditor/commit/09b3c85f3466bac2bdba254032137dc058424449))
* 事件解绑 ([b9cc154](https://github.com/wangeditor-team/wangEditor/commit/b9cc15453e6b2a5717968e880e1bc2b4e550ed5d))
* 事件解绑 ([cedacb7](https://github.com/wangeditor-team/wangEditor/commit/cedacb78787770c6752fe2f7600018a7f412666c))
* 修复append不触发change ([8daefb2](https://github.com/wangeditor-team/wangEditor/commit/8daefb2bee9e630dd50b01908deed7fc4f4456dc))
* 修复insertAfter方法当插入多个元素时插入后元素间顺序颠倒的问题 ([69c6e0c](https://github.com/wangeditor-team/wangEditor/commit/69c6e0c5fa0452887f198c407aefe037aefca96e))
* 编写注释 和 注释暂时无用的代码 ([56f0536](https://github.com/wangeditor-team/wangEditor/commit/56f05362c674eae74b2778b7bc8833b15245c184))
* 菜单栏换行，droplist无法选择 ([bd7353b](https://github.com/wangeditor-team/wangEditor/commit/bd7353b9290fdd57ef08fb82e65ad225f8089b63))
* 零散问题 ([b56a017](https://github.com/wangeditor-team/wangEditor/commit/b56a0178434a7b5f18a120d34290d73f34db6caf))
* 默认文本placeholder修复 ([686cb7d](https://github.com/wangeditor-team/wangEditor/commit/686cb7d68d7c9a315cfc5cd4416b8c14f468b842))


### Features

* 增加 editor.config.height ([bf96b29](https://github.com/wangeditor-team/wangEditor/commit/bf96b29c836b36802c3e78109e5abce15bbcf0a3))
* 撤销功能 重做功能 ([8bb65db](https://github.com/wangeditor-team/wangEditor/commit/8bb65dbd47f53b84c3caba02905bd886585fe087))
* 编辑器销毁 ([8b4706c](https://github.com/wangeditor-team/wangEditor/commit/8b4706c688dbaac2ad3faa3d84e34c99fb6b1997))



## [0.0.22](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-08-10)


### Bug Fixes

* demo 名字修改 ([d3f506f](https://github.com/wangeditor-team/wangEditor/commit/d3f506f63d2acf210e9b4925a9855d99f3cfec33))
* editor.change() ([8775826](https://github.com/wangeditor-team/wangEditor/commit/8775826a7bf39ee886203e02c0a3c87b439474ed))
* i18next cdn 应用 到 demo ([b2e706e](https://github.com/wangeditor-team/wangEditor/commit/b2e706e432b924dcc4f220dda9871ff1ec1657c4))
* link tooltip i18n ([483ca9f](https://github.com/wangeditor-team/wangEditor/commit/483ca9fd82a2b0545fbe51cbc0eb9cd1ea089679))
* 优化代码 ([be2b4ed](https://github.com/wangeditor-team/wangEditor/commit/be2b4ed2fe2136725c6fcc0a8f97a271c7a14ecb))
* 优化写法切换显示placeholder ([835d943](https://github.com/wangeditor-team/wangEditor/commit/835d9439b523c99bbfffc252159a440a142e19ab))
* 修改 如 的 翻译 ([8ea56c1](https://github.com/wangeditor-team/wangEditor/commit/8ea56c1d127ea65e9c04d8cf23e229282985b884))
* 修改文件位置 格式 测试用例 demo 文件 英文翻译 等 ([be837f4](https://github.com/wangeditor-team/wangEditor/commit/be837f4a5a4f3d8ac5cadd475c5d65780cd4f271))
* 修改翻译 ([b8dec34](https://github.com/wangeditor-team/wangEditor/commit/b8dec34d40404c48b15fea652b71309baabd90b9))
* 居中的翻译 Resource interface 改 type i18next.html ([3a790e5](https://github.com/wangeditor-team/wangEditor/commit/3a790e52af0483332259eaeeeb5d3b0af14cbd84))
* 手动触发change事件来切换placeholder ([501c217](https://github.com/wangeditor-team/wangEditor/commit/501c21772d957fd7321104ed16472220717d2d05))
* 文件重命名 ([44f65c2](https://github.com/wangeditor-team/wangEditor/commit/44f65c2fea4aa3e7c062a64b15231b81ef9feb43))
* 样式文件调整路径 ([6f38519](https://github.com/wangeditor-team/wangEditor/commit/6f38519d96219642e5f376bf54dd40aa69426be7))
* 添加翻译 ([4ce3229](https://github.com/wangeditor-team/wangEditor/commit/4ce32290f17bddbbc45c9cf0ba9ae772df193aa2))


### Features

* i18next 功能 初步 ([cf8d4c8](https://github.com/wangeditor-team/wangEditor/commit/cf8d4c8b7086b6231586d4ab3fdda16f83585629))
* 中英文翻译格式，以及目前的菜单都加上了 翻译 ([585d6d6](https://github.com/wangeditor-team/wangEditor/commit/585d6d6a75db84170e331ffd028ba5afc13d1f56))
* 添加可配置placeholder ([1ee4348](https://github.com/wangeditor-team/wangEditor/commit/1ee4348e57f4b94e539f5a4920c07f7f63bf18e8))
* 菜单栏换行 ([790f02e](https://github.com/wangeditor-team/wangEditor/commit/790f02e5d59887b29d94d475bca6d9224bf8dcaf))



## [0.0.21](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-08-03)


### Bug Fixes

* delete console ([4f45fd8](https://github.com/wangeditor-team/wangEditor/commit/4f45fd8309357f6f43d1bb397215eaeab83cedc7))
* imgClickHook写法 ([ad76889](https://github.com/wangeditor-team/wangEditor/commit/ad7688905f655c0ef00a4edb8d6a84af54089ff6))
* keyup隐藏图片拖拽框 ([f35fd74](https://github.com/wangeditor-team/wangEditor/commit/f35fd74b64929e3f54607c215bdc8e49660beddc))
* 优化eventHooks写法 ([0c86917](https://github.com/wangeditor-team/wangEditor/commit/0c86917209cb5b9fae4babd42596221d1a24eb5d))
* 修改 getNodeTop 和 getSelctionRangeTopNodes ([b535a84](https://github.com/wangeditor-team/wangEditor/commit/b535a84547ca81bc61886c6cec6d88e7cf74b9c8))
* 修改 saveRange 的注释 ([f5fd204](https://github.com/wangeditor-team/wangEditor/commit/f5fd204b20e8124f89006074769f5f21b7d2d8d4))
* 修改 取消 判断 ([6579f88](https://github.com/wangeditor-team/wangEditor/commit/6579f88250a14a6a54ddcb779fb11429da573d9d))
* 删除 获取段落 和 选取段落的 文件 因为已经提升到 公用方法里了 ([b90d270](https://github.com/wangeditor-team/wangEditor/commit/b90d27082a15617503f7a7c743ddba3bb7f42093))
* 删除无用引用 ([559002a](https://github.com/wangeditor-team/wangEditor/commit/559002aa0e1e8c63d3fa8b1d4b64841f39dea130))
* 删除无用的文件和注释 ([ec3bfa3](https://github.com/wangeditor-team/wangEditor/commit/ec3bfa3fbaa088c5ec65c3ca296a98896d3b0bfd))
* 参考 v3 列表实现 ([cd4a85f](https://github.com/wangeditor-team/wangEditor/commit/cd4a85f9ab277e25ad05e9ab2b902c420ce225ba))
* 多段落创建 列表 ([f350311](https://github.com/wangeditor-team/wangEditor/commit/f3503111d8a3ed6c423faa429227a7d2a751ae0c))
* 数据类型 ([cc51ab1](https://github.com/wangeditor-team/wangEditor/commit/cc51ab118a8f2c6f152f0705f83f5297d43a750c))
* 添加额外的默认字体 ([7f1b926](https://github.com/wangeditor-team/wangEditor/commit/7f1b9262d2fdef09ec89f67af495dcdc788f387c))
* 设置序列 和 取消序列 和 序列互相转换 ([3f772c5](https://github.com/wangeditor-team/wangEditor/commit/3f772c501ca97b0001c1b35dc09992fcee32533c))
* 高亮 ([6e06561](https://github.com/wangeditor-team/wangEditor/commit/6e065610baacf883371f40a23516712214352bd1))


### Features

*  有序列表 无序列表 ([55ec5f3](https://github.com/wangeditor-team/wangEditor/commit/55ec5f38d7bbe98d326fed120817b0f37764c859))
* 序列 ([cc4d3f4](https://github.com/wangeditor-team/wangEditor/commit/cc4d3f4208008106ac867be7f8e096acd0894f29))
* 拖拽图片尺寸 ([0744041](https://github.com/wangeditor-team/wangEditor/commit/07440417088548bd1117491bfd942b0d52959c05))
* 新增行高功能 ([52bfc08](https://github.com/wangeditor-team/wangEditor/commit/52bfc086b5ba1fa8688640ca2efd227cc7141811))
* 样式文件build部署报错 ([794c4ba](https://github.com/wangeditor-team/wangEditor/commit/794c4bafc0d3030aa0b6f8c22bce802a69051768))
* 添加 获取 元素段落方法 和 获取当前选取范围内的段落 方法 ([2b72c6d](https://github.com/wangeditor-team/wangEditor/commit/2b72c6d0bf4059bac6bf181d9667430329419008))
* 添加新的 dom-core 方法 ([35879fb](https://github.com/wangeditor-team/wangEditor/commit/35879fba28c63b7963eaea961f6f78e25fc22eb7))



## [0.0.20](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-07-24)


### Bug Fixes

* dropListConf 宽度 ([a9fcc8d](https://github.com/wangeditor-team/wangEditor/commit/a9fcc8d214ed5eb2ae639663517122fd55dfcb5f))
* ts 类型问题 ([2d85266](https://github.com/wangeditor-team/wangEditor/commit/2d85266a788c36c668d79601d2ac65efdec09847))
* 修改 css class 名称 ([9ff1ffd](https://github.com/wangeditor-team/wangEditor/commit/9ff1ffdc7be476511f5cbef260c581f6e3c77afe))
* 修改 css class 名称 ([3bec8a2](https://github.com/wangeditor-team/wangEditor/commit/3bec8a2ed49ee8f3226e34d83708684929a4b3cc))
* 修改 css 替换style为空的时候，会���残留 ([6a56477](https://github.com/wangeditor-team/wangEditor/commit/6a564774a198bd450997250cfc869eaf7eab940f))
* 修改减少缩进的名称 和 icon 统一 ([37d965a](https://github.com/wangeditor-team/wangEditor/commit/37d965adbe3cb25615e506d1fdf1e3db054ae8f2))
* 修改字体颜色 ([2a84f0b](https://github.com/wangeditor-team/wangEditor/commit/2a84f0be1d8a6ff17294c8c0e5ae5cc8d7eff6b7))
* 函数添加返回值 ([2fd126a](https://github.com/wangeditor-team/wangEditor/commit/2fd126a8d3b557e0cbe0ea518ae06c25eef4c43a))
* 多段落的选区节点获取段落 ([296d543](https://github.com/wangeditor-team/wangEditor/commit/296d543b20479bc01f78a3bd978ea511e2f2289a))
* 添加返回值类型 ([815df3d](https://github.com/wangeditor-team/wangEditor/commit/815df3da8e5d29a4c115c301dc43df5288610820))
* 激活样式 ([676c4e5](https://github.com/wangeditor-team/wangEditor/commit/676c4e511764554b51256ffc681c5e749bdc7b5b))
* 限定只有p标签为段落 ([2f95a76](https://github.com/wangeditor-team/wangEditor/commit/2f95a764c59e78b41ee01cf7eae09e8d4ee80b19))


### Features

* 拖拽上传图片 ([947d22d](https://github.com/wangeditor-team/wangEditor/commit/947d22daa73cd48c0b9c7cdfaece9664474c8912))
* 粘贴上传图片 ([58d1c82](https://github.com/wangeditor-team/wangEditor/commit/58d1c82a6f75af72525b7463060d56ae5d71f9a0))
* 自定义菜单 ([60c4e89](https://github.com/wangeditor-team/wangEditor/commit/60c4e8948144402a0a4f9e3231aac560ad369731))



## [0.0.19](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-07-24)


### Features

* 新增 icon 样式 图标 ([adbf5d1](https://github.com/wangeditor-team/wangEditor/commit/adbf5d19d9b520efd94cdca8818fb5ccb54d33e1))



## [0.0.18](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-07-17)


### Bug Fixes

* 发布 npm 之后自动部署 ([65f9157](https://github.com/wangeditor-team/wangEditor/commit/65f9157535104373baf2e15ac6bc02b2026b2743))



## [0.0.17](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-07-17)


### Bug Fixes

* npm 发布之后自动部署 ([a553f77](https://github.com/wangeditor-team/wangEditor/commit/a553f77dac48b250422e4191546d82840ce3a9aa))



## [0.0.16](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-07-17)


### Bug Fixes

* npm 发布后自动部署 ([17cfd5e](https://github.com/wangeditor-team/wangEditor/commit/17cfd5e1b7803428d209c488057820dabced7347))



## [0.0.15](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-07-17)



## [0.0.14](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-07-17)


### Bug Fixes

* 上传图片 API ([16c50a2](https://github.com/wangeditor-team/wangEditor/commit/16c50a2de6b53589f83a0840560836207e946712))
* 上传图片配置项的零散问题 ([b96c2a9](https://github.com/wangeditor-team/wangEditor/commit/b96c2a9c77f4f3f21b5a8fc0be2f8849135fdd46))
* 文字修改 ([f69c1f7](https://github.com/wangeditor-team/wangEditor/commit/f69c1f74f71ca69766d848b6ff266c0dc49f52b3))


### Features

* 上传图片，基本功能 ([3c26ed2](https://github.com/wangeditor-team/wangEditor/commit/3c26ed2b37495837f228a298f4f0473a3be496f5))
* 插入网络图片 ([9dc8eb0](https://github.com/wangeditor-team/wangEditor/commit/9dc8eb0195baf0d62fac1d992868e38c6584c342))
* 视频 ([b00bdf3](https://github.com/wangeditor-team/wangEditor/commit/b00bdf30a3b9e803e251da633e883196cb4524b6))



## [0.0.13](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-07-13)


### Bug Fixes

* github actions ([b2ad5a7](https://github.com/wangeditor-team/wangEditor/commit/b2ad5a732563f67434f6e5584b23d5b34b81e025))
* github actions ([17e51cd](https://github.com/wangeditor-team/wangEditor/commit/17e51cde89b1ff0a22b51cb64b6ebe86257c8e71))
* github actions ([f137c6b](https://github.com/wangeditor-team/wangEditor/commit/f137c6b7e6aa02287b4592d8932f5dabdd55c0f6))
* github actions ([0dbfed2](https://github.com/wangeditor-team/wangEditor/commit/0dbfed27269f05f8735d2113b3cbdf0507cdbd41))
* github actions ([0d11d7a](https://github.com/wangeditor-team/wangEditor/commit/0d11d7a70bc39e44532f3736a8602597ec2591a1))
* github actions ([995f63a](https://github.com/wangeditor-team/wangEditor/commit/995f63aaba5f38107b84fc4d7d9e3355b1e09f42))
* github actions 连接腾讯云服务器 ([5ed2c94](https://github.com/wangeditor-team/wangEditor/commit/5ed2c949945295baf9561eff2b770b4cf0a96853))
* 修改注释 ([6d187b3](https://github.com/wangeditor-team/wangEditor/commit/6d187b3bd5dd19835a6e5a15c5bf96f7cce2751e))
* 点击事件可以撤销引用 & 菜单激活优化 ([19a930a](https://github.com/wangeditor-team/wangEditor/commit/19a930afe33feea993afe1e6ef233092edcd25b3))
* 菜单激活状态判断 ([ab0dae0](https://github.com/wangeditor-team/wangEditor/commit/ab0dae0b72fd73d7f6e34ad1294a3dd6922ea234))


### Features

* 引用菜单 ([94955ce](https://github.com/wangeditor-team/wangEditor/commit/94955ce5ea9534bad5f58e99c22ea46bea033970))



## [0.0.12](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-07-09)


### Bug Fixes

* customConfig ([4c8b743](https://github.com/wangeditor-team/wangEditor/commit/4c8b7433ce2fadd91b78b832c97af283c15f6945))
* package.json 命令格式 ([a5d3f33](https://github.com/wangeditor-team/wangEditor/commit/a5d3f33525278904287a658ab9af9595472ca0bf))
* 修改配置菜单类型错误 ([3cc437f](https://github.com/wangeditor-team/wangEditor/commit/3cc437fcdbfd789d1a52c54b297307ce82f4b8e7))
* 修改配置菜单类型错误 18:43 ([79f8e98](https://github.com/wangeditor-team/wangEditor/commit/79f8e98011ca94701851192bd903d4cb0a81bd0c))


### Features

* 修改字号添加 ([8f84196](https://github.com/wangeditor-team/wangEditor/commit/8f8419609268ff188008cd3321d06e3018d7eab6))



## [0.0.11](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-07-06)


### Bug Fixes

* blur focs ([d230f4d](https://github.com/wangeditor-team/wangEditor/commit/d230f4d488e688178d41780517dfe902c42d358b))
* type DropListItem 重复定义 ([868fc7e](https://github.com/wangeditor-team/wangEditor/commit/868fc7e641380e9cddbf72b2982e76ddaf1e0dc0))
* 修改链接 删除链接 ([e984cc6](https://github.com/wangeditor-team/wangEditor/commit/e984cc6373a2044360ed6368e5cde2bc93a3beda))
* 单词拼写 ([e43ac56](https://github.com/wangeditor-team/wangEditor/commit/e43ac56cdc6b7a31bf35211216e0751c45338e75))


### Features

* tooltip 和查案链接 ([ff58176](https://github.com/wangeditor-team/wangEditor/commit/ff58176fe590775f0d46dc946745d236be146a76))
* 上传图片 API ([ed847a4](https://github.com/wangeditor-team/wangEditor/commit/ed847a45adbf81a7492ef66f4614eda8fe27a3de))
* 功能justify ([095952f](https://github.com/wangeditor-team/wangEditor/commit/095952f31d19e6ec36b32cd6e2df6087efb909f0))
* 增加 server ([46eec4e](https://github.com/wangeditor-team/wangEditor/commit/46eec4e454a350c95cdeb5a3c1eb0550fe50c3b8))



## [0.0.10](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-07-06)



## [0.0.9](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-06-23)


### Bug Fixes

* 发布 npm ([f5274a2](https://github.com/wangeditor-team/wangEditor/commit/f5274a252b3012c784a3d5166454a1f6972db7aa))



## [0.0.8](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-06-23)


### Bug Fixes

* 粘贴忽略 svg ([eca56f3](https://github.com/wangeditor-team/wangEditor/commit/eca56f35c9a07ec32d4a9a1a2e4d1ad62998b4c8))


### Features

* 增加 txt.getJSON API ([09030ce](https://github.com/wangeditor-team/wangEditor/commit/09030ce733a2c74c8ded1e034431eef8c94b321a))
* 增加配置 styleWIthCSS ([b04adc2](https://github.com/wangeditor-team/wangEditor/commit/b04adc24a7d0d6e7327d74c0ac1fd984b603b95c))
* 处理 tab ([77974cd](https://github.com/wangeditor-team/wangEditor/commit/77974cd3ad8a69521ada0845c7aaa9bd0aed621c))
* 粘贴文本和 html ([51157a7](https://github.com/wangeditor-team/wangEditor/commit/51157a709c020eeb14ca70d5a99a2fdc45ddaa46))



## [0.0.7](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-06-11)


### Bug Fixes

* github 安全提示 ([3baab59](https://github.com/wangeditor-team/wangEditor/commit/3baab59d572f876d16950a4302ef8326ddc94cc4))



## [0.0.6](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-06-09)



## [0.0.5](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-06-09)



## [0.0.4](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-06-09)



## [0.0.3](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-06-09)



## [0.0.2](https://github.com/wangeditor-team/wangEditor/compare/v4.0.8...v4.1.0) (2020-06-09)


### Bug Fixes

* interface 改为 type ([3a617eb](https://github.com/wangeditor-team/wangEditor/commit/3a617ebd9c9a773439f3e47ee337b3b1db3048eb))
* npm adit fix ([f868b3a](https://github.com/wangeditor-team/wangEditor/commit/f868b3a970acd00952d4e99038c080fb327aad99))


### Features

* Bold ([adc279b](https://github.com/wangeditor-team/wangEditor/commit/adc279b214fc503d34cbfd482a9035bb60d91ff3))
* DropListMenu ([d5a1e5c](https://github.com/wangeditor-team/wangEditor/commit/d5a1e5c4ef5ea06bd7a2f2987ff04447833f680f))
* Link menu ([5dc3398](https://github.com/wangeditor-team/wangEditor/commit/5dc3398f355796408050147b6286dd04a72d5e63))
* 初始化编辑器的若干操作 ([84d6c03](https://github.com/wangeditor-team/wangEditor/commit/84d6c03496dad92814412b6563c4390422eadbe3))

