import _Object$assign from "core-js-pure/features/object/assign.js";
import _bindInstanceProperty from "core-js-pure/features/instance/bind.js";
function _extends() {
  var _context;
  return _extends = _Object$assign ? _bindInstanceProperty(_context = _Object$assign).call(_context) : function (n) {
    for (var e = 1; e < arguments.length; e++) {
      var t = arguments[e];
      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);
    }
    return n;
  }, _extends.apply(null, arguments);
}
export { _extends as default };