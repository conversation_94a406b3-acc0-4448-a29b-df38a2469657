export default {
    lang: 'zh-CN',
    languages: {
        'zh-CN': {
            wangEditor: {
                重置: '重置',
                插入: '插入',
                默认: '默认',
                创建: '创建',
                修改: '修改',
                如: '如',
                请输入正文: '请输入正文',
                menus: {
                    title: {
                        标题: '标题',
                        加粗: '加粗',
                        字号: '字号',
                        字体: '字体',
                        斜体: '斜体',
                        下划线: '下划线',
                        删除线: '删除线',
                        缩进: '缩进',
                        行高: '行高',
                        文字颜色: '文字颜色',
                        背景色: '背景色',
                        链接: '链接',
                        序列: '序列',
                        对齐: '对齐',
                        引用: '引用',
                        表情: '表情',
                        图片: '图片',
                        视频: '视频',
                        表格: '表格',
                        代码: '代码',
                        分割线: '分割线',
                        恢复: '恢复',
                        撤销: '撤销',
                        全屏: '全屏',
                        取消全屏: '取消全屏',
                        待办事项: '待办事项',
                    },
                    dropListMenu: {
                        设置标题: '设置标题',
                        背景颜色: '背景颜色',
                        文字颜色: '文字颜色',
                        设置字号: '设置字号',
                        设置字体: '设置字体',
                        设置缩进: '设置缩进',
                        对齐方式: '对齐方式',
                        设置行高: '设置行高',
                        序列: '序列',
                        head: {
                            正文: '正文',
                        },
                        indent: {
                            增加缩进: '增加缩进',
                            减少缩进: '减少缩进',
                        },
                        justify: {
                            靠左: '靠左',
                            居中: '居中',
                            靠右: '靠右',
                            两端: '两端',
                        },
                        list: {
                            无序列表: '无序列表',
                            有序列表: '有序列表',
                        },
                    },
                    panelMenus: {
                        emoticon: {
                            默认: '默认',
                            新浪: '新浪',
                            emoji: 'emoji',
                            手势: '手势',
                        },
                        image: {
                            上传图片: '上传图片',
                            网络图片: '网络图片',
                            图片地址: '图片地址',
                            图片文字说明: '图片文字说明',
                            跳转链接: '跳转链接',
                        },
                        link: {
                            链接: '链接',
                            链接文字: '链接文字',
                            取消链接: '取消链接',
                            查看链接: '查看链接',
                        },
                        video: {
                            插入视频: '插入视频',
                            上传视频: '上传视频',
                        },
                        table: {
                            行: '行',
                            列: '列',
                            的: '的',
                            表格: '表格',
                            添加行: '添加行',
                            删除行: '删除行',
                            添加列: '添加列',
                            删除列: '删除列',
                            设置表头: '设置表头',
                            取消表头: '取消表头',
                            插入表格: '插入表格',
                            删除表格: '删除表格',
                        },
                        code: {
                            删除代码: '删除代码',
                            修改代码: '修改代码',
                            插入代码: '插入代码',
                        },
                    },
                },
                validate: {
                    张图片: '张图片',
                    大于: '大于',
                    图片链接: '图片链接',
                    不是图片: '不是图片',
                    返回结果: '返回结果',
                    上传图片超时: '上传图片超时',
                    上传图片错误: '上传图片错误',
                    上传图片失败: '上传图片失败',
                    插入图片错误: '插入图片错误',
                    一次最多上传: '一次最多上传',
                    下载链接失败: '下载链接失败',
                    图片验证未通过: '图片验证未通过',
                    服务器返回状态: '服务器返回状态',
                    上传图片返回结果错误: '上传图片返回结果错误',
                    请替换为支持的图片类型: '请替换为支持的图片类型',
                    您插入的网络图片无法识别: '您插入的网络图片无法识别',
                    您刚才插入的图片链接未通过编辑器校验: '您刚才插入的图片链接未通过编辑器校验',
                    插入视频错误: '插入视频错误',
                    视频链接: '视频链接',
                    不是视频: '不是视频',
                    视频验证未通过: '视频验证未通过',
                    个视频: '个视频',
                    上传视频超时: '上传视频超时',
                    上传视频错误: '上传视频错误',
                    上传视频失败: '上传视频失败',
                    上传视频返回结果错误: '上传视频返回结果错误',
                },
            },
        },
        en: {
            wangEditor: {
                重置: 'reset',
                插入: 'insert',
                默认: 'default',
                创建: 'create',
                修改: 'edit',
                如: 'like',
                请输入正文: 'please enter the text',
                menus: {
                    title: {
                        标题: 'head',
                        加粗: 'bold',
                        字号: 'font size',
                        字体: 'font family',
                        斜体: 'italic',
                        下划线: 'underline',
                        删除线: 'strikethrough',
                        缩进: 'indent',
                        行高: 'line heihgt',
                        文字颜色: 'font color',
                        背景色: 'background',
                        链接: 'link',
                        序列: 'numbered list',
                        对齐: 'align',
                        引用: 'quote',
                        表情: 'emoticons',
                        图片: 'image',
                        视频: 'media',
                        表格: 'table',
                        代码: 'code',
                        分割线: 'split line',
                        恢复: 'redo',
                        撤销: 'undo',
                        全屏: 'fullscreen',
                        取消全屏: 'cancel fullscreen',
                        待办事项: 'todo',
                    },
                    dropListMenu: {
                        设置标题: 'title',
                        背景颜色: 'background',
                        文字颜色: 'font color',
                        设置字号: 'font size',
                        设置字体: 'font family',
                        设置缩进: 'indent',
                        对齐方式: 'align',
                        设置行高: 'line heihgt',
                        序列: 'list',
                        head: {
                            正文: 'text',
                        },
                        indent: {
                            增加缩进: 'indent',
                            减少缩进: 'outdent',
                        },
                        justify: {
                            靠左: 'left',
                            居中: 'center',
                            靠右: 'right',
                            两端: 'justify',
                        },
                        list: {
                            无序列表: 'unordered',
                            有序列表: 'ordered',
                        },
                    },
                    panelMenus: {
                        emoticon: {
                            表情: 'emoji',
                            手势: 'gesture',
                        },
                        image: {
                            上传图片: 'upload image',
                            网络图片: 'network image',
                            图片地址: 'image link',
                            图片文字说明: 'image alt',
                            跳转链接: 'hyperlink',
                        },
                        link: {
                            链接: 'link',
                            链接文字: 'link text',
                            取消链接: 'unlink',
                            查看链接: 'view links',
                        },
                        video: {
                            插入视频: 'insert video',
                            上传视频: 'upload local video',
                        },
                        table: {
                            行: 'rows',
                            列: 'columns',
                            的: ' ',
                            表格: 'table',
                            添加行: 'insert row',
                            删除行: 'delete row',
                            添加列: 'insert column',
                            删除列: 'delete column',
                            设置表头: 'set header',
                            取消表头: 'cancel header',
                            插入表格: 'insert table',
                            删除表格: 'delete table',
                        },
                        code: {
                            删除代码: 'delete code',
                            修改代码: 'edit code',
                            插入代码: 'insert code',
                        },
                    },
                },
                validate: {
                    张图片: 'images',
                    大于: 'greater than',
                    图片链接: 'image link',
                    不是图片: 'is not image',
                    返回结果: 'return results',
                    上传图片超时: 'upload image timeout',
                    上传图片错误: 'upload image error',
                    上传图片失败: 'upload image failed',
                    插入图片错误: 'insert image error',
                    一次最多上传: 'once most at upload',
                    下载链接失败: 'download link failed',
                    图片验证未通过: 'image validate failed',
                    服务器返回状态: 'server return status',
                    上传图片返回结果错误: 'upload image return results error',
                    请替换为支持的图片类型: 'please replace with a supported image type',
                    您插入的网络图片无法识别: 'the network picture you inserted is not recognized',
                    您刚才插入的图片链接未通过编辑器校验:
                        'the image link you just inserted did not pass the editor verification',
                    插入视频错误: 'insert video error',
                    视频链接: 'video link',
                    不是视频: 'is not video',
                    视频验证未通过: 'video validate failed',
                    个视频: 'videos',
                    上传视频超时: 'upload video timeout',
                    上传视频错误: 'upload video error',
                    上传视频失败: 'upload video failed',
                    上传视频返回结果错误: 'upload video return results error',
                },
            },
        },
    },
}
